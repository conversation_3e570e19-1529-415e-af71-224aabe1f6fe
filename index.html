<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>الخليل وآيات 💖</title>
  <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Cairo', 'Arial', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
      color: #333;
      text-align: center;
      padding: 2rem 1rem;
      min-height: 100vh;
      position: relative;
      overflow-x: hidden;
    }

    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.1)">💖</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
      pointer-events: none;
      z-index: -1;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    h1 {
      font-family: 'Amiri', serif;
      font-size: 3.5rem;
      font-weight: 700;
      color: #fff;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      margin-bottom: 2rem;
      animation: float 3s ease-in-out infinite;
      background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: float 3s ease-in-out infinite, gradientShift 8s ease infinite;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      padding: 0 1rem;
    }

    .card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 25px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2);
      padding: 2.5rem 2rem;
      margin: 1rem 0;
      position: relative;
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      animation: fadeInUp 0.8s ease forwards;
      border: 1px solid rgba(255,255,255,0.3);
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
      transition: left 0.5s;
    }

    .card:hover::before {
      left: 100%;
    }

    .card:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 30px 60px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.3);
    }

    .card h2 {
      font-family: 'Amiri', serif;
      font-size: 1.8rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 1.5rem;
      position: relative;
    }

    .card h2::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      border-radius: 2px;
    }

    .big {
      font-size: 1.8rem;
      font-weight: 600;
      background: linear-gradient(45deg, #e74c3c, #f39c12, #9b59b6, #3498db);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradientShift 6s ease infinite;
      line-height: 1.4;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .note {
      color: #7f8c8d;
      font-style: italic;
      margin-top: 15px;
      font-size: 1.1rem;
    }

    .special-card {
      background: linear-gradient(135deg, rgba(255,107,107,0.1), rgba(254,202,87,0.1));
      border: 2px solid rgba(255,107,107,0.3);
    }

    .promise-card {
      background: linear-gradient(135deg, rgba(155,89,182,0.1), rgba(52,152,219,0.1));
      border: 2px solid rgba(155,89,182,0.3);
    }

    @media (max-width: 768px) {
      h1 {
        font-size: 2.5rem;
      }

      .container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .card {
        padding: 2rem 1.5rem;
      }

      .big {
        font-size: 1.5rem;
      }
    }

    .floating-hearts {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .heart {
      position: absolute;
      font-size: 20px;
      color: rgba(255,255,255,0.3);
      animation: floatUp 8s linear infinite;
      transition: all 0.3s ease;
    }

    @keyframes floatUp {
      0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 1;
      }
      100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
      }
    }

    /* تأثيرات الموسيقى */
    .music-reactive {
      transition: all 0.1s ease;
    }

    .music-controls {
      position: fixed;
      bottom: 30px;
      right: 30px;
      z-index: 1000;
      display: flex;
      gap: 15px;
      align-items: center;
      background: rgba(255,255,255,0.1);
      backdrop-filter: blur(20px);
      border-radius: 50px;
      padding: 15px 20px;
      border: 1px solid rgba(255,255,255,0.2);
    }

    .music-btn {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: none;
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      color: white;
      font-size: 20px;
      cursor: pointer;
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .music-btn:hover {
      transform: scale(1.1);
      box-shadow: 0 12px 30px rgba(0,0,0,0.3);
    }

    .music-btn.playing {
      animation: pulse 2s ease-in-out infinite;
    }

    .volume-control {
      width: 80px;
      height: 5px;
      background: rgba(255,255,255,0.3);
      border-radius: 5px;
      outline: none;
      cursor: pointer;
      appearance: none;
    }

    .volume-control::-webkit-slider-thumb {
      appearance: none;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      cursor: pointer;
      box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    }

    .music-visualizer {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 80px;
      pointer-events: none;
      z-index: -1;
      display: flex;
      align-items: end;
      justify-content: center;
      gap: 2px;
      padding: 0 20px;
      opacity: 0.6;
    }

    .visualizer-bar {
      width: 3px;
      background: linear-gradient(to top, #ff6b6b, #feca57, #48dbfb);
      border-radius: 2px;
      transition: height 0.1s ease;
      min-height: 5px;
    }

    .pulse-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 200px;
      border: 2px solid rgba(255,255,255,0.2);
      border-radius: 50%;
      animation: pulse-ring 4s ease-in-out infinite;
      pointer-events: none;
    }

    .pulse-ring:nth-child(2) {
      width: 300px;
      height: 300px;
      animation-delay: 1s;
      opacity: 0.5;
    }

    .pulse-ring:nth-child(3) {
      width: 400px;
      height: 400px;
      animation-delay: 2s;
      opacity: 0.3;
    }

    @keyframes pulse-ring {
      0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
      }
      50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.3;
      }
      100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
      }
    }

    .music-particles {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: radial-gradient(circle, rgba(255,107,107,0.8), transparent);
      border-radius: 50%;
      animation: particle-float 8s linear infinite;
    }

    .particle:nth-child(2n) {
      background: radial-gradient(circle, rgba(254,202,87,0.8), transparent);
      animation-duration: 10s;
    }

    .particle:nth-child(3n) {
      background: radial-gradient(circle, rgba(72,219,251,0.8), transparent);
      animation-duration: 12s;
    }

    @keyframes particle-float {
      0% {
        transform: translateY(100vh) translateX(0) scale(0) rotate(0deg);
        opacity: 0;
      }
      10% {
        opacity: 1;
        transform: scale(1);
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateY(-100px) translateX(50px) scale(0) rotate(360deg);
        opacity: 0;
      }
    }

    .music-btn:hover {
      background: rgba(255,255,255,0.3);
      transform: scale(1.05);
    }

    .music-btn.playing {
      background: rgba(255,107,107,0.3);
      border-color: rgba(255,107,107,0.5);
    }

    .visualizer {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100px;
      pointer-events: none;
      z-index: -1;
      display: flex;
      align-items: end;
      justify-content: center;
      gap: 3px;
      padding: 0 20px;
    }

    .bar {
      width: 4px;
      background: linear-gradient(to top, #ff6b6b, #feca57, #48dbfb);
      border-radius: 2px;
      transition: height 0.1s ease;
      opacity: 0.7;
    }

    .music-particles {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .particle {
      position: absolute;
      width: 6px;
      height: 6px;
      background: radial-gradient(circle, #ff6b6b, transparent);
      border-radius: 50%;
      animation: particleFloat 4s ease-in-out infinite;
    }

    @keyframes particleFloat {
      0%, 100% {
        transform: translateY(0) scale(0.5);
        opacity: 0;
      }
      50% {
        transform: translateY(-50px) scale(1);
        opacity: 1;
      }
    }

    .pulse-ring {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 200px;
      border: 2px solid rgba(255,255,255,0.3);
      border-radius: 50%;
      pointer-events: none;
      z-index: -1;
      animation: pulseRing 3s ease-in-out infinite;
    }

    @keyframes pulseRing {
      0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
      }
      100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
      }
    }

    .dancing-text {
      animation: textDance 2s ease-in-out infinite;
    }

    @keyframes textDance {
      0%, 100% { transform: translateY(0); }
      25% { transform: translateY(-5px); }
      75% { transform: translateY(5px); }
    }
  </style>
</head>
<body>
  <!-- عناصر التأثيرات البصرية -->
  <div class="floating-hearts" id="floatingHearts"></div>
  <div class="music-particles" id="musicParticles"></div>
  <div class="music-visualizer" id="musicVisualizer"></div>

  <!-- حلقات النبض -->
  <div class="pulse-ring"></div>
  <div class="pulse-ring"></div>
  <div class="pulse-ring"></div>

  <!-- عناصر التحكم بالموسيقى -->
  <div class="music-controls">
    <button class="music-btn" id="playPauseBtn" title="تشغيل/إيقاف الموسيقى">
      ▶️
    </button>
    <input type="range" class="volume-control" id="volumeControl" min="0" max="100" value="50" title="مستوى الصوت">
    <button class="music-btn" id="muteBtn" title="كتم الصوت">
      🔊
    </button>
  </div>

  <!-- ملف الصوت -->
  <audio id="backgroundMusic" loop preload="auto">
    <!-- ضع ملفات MP3 في مجلد music -->
    <source src="music/romantic-music.mp3" type="audio/mpeg">
    <source src="music/love-song.mp3" type="audio/mpeg">
    <source src="music/background-music.mp3" type="audio/mpeg">

    <!-- أو في نفس المجلد مباشرة -->
    <source src="romantic-music.mp3" type="audio/mpeg">
    <source src="love-song.mp3" type="audio/mpeg">
    <source src="background-music.mp3" type="audio/mpeg">

    <!-- سيتم إنشاء نغمة بديلة بـ JavaScript إذا لم تعمل الملفات -->
  </audio>

  <h1 class="music-reactive">💖 الخليل وآيات 💖</h1>

  <div class="container">
    <div class="card" style="animation-delay: 0.2s;">
      <h2>✨ منذ لقائنا الأول</h2>
      <div class="note">27 يونيو 2025</div>
      <div id="sinceMeeting" class="big"></div>
    </div>

    <div class="card" style="animation-delay: 0.4s;">
      <h2>🎂 عيد ميلاد آيات الحبيبة</h2>
      <div class="note">8 نوفمبر 1999</div>
      <div id="ayaCountdown" class="big"></div>
    </div>

    <div class="card" style="animation-delay: 0.6s;">
      <h2>🎉 عيد ميلاد الخليل العزيز</h2>
      <div class="note">28 أكتوبر 1999</div>
      <div id="khalilCountdown" class="big"></div>
    </div>

    <div class="card special-card" style="animation-delay: 0.8s;">
      <h2>� حقيقة جميلة</h2>
      <div class="big">الخليل أكبر من آيات بـ 11 يوم فقط 👑�</div>
      <div class="note">فارق عمر رومانسي وجميل</div>
    </div>

    <div class="card promise-card" style="animation-delay: 1s;">
      <h2>💍 عهد الحب الأبدي</h2>
      <div class="big">معاً نحو المستقبل... الزواج والسعادة والحب الصادق 💖</div>
      <div class="note">وعد بالحب والاحترام والإخلاص</div>
    </div>

    <div class="card" style="animation-delay: 1.2s;">
      <h2>🌟 رسالة حب</h2>
      <div class="big">كل لحظة معك هي ذكرى جميلة... وكل يوم قادم هو حلم نحققه سوياً 💕</div>
    </div>
  </div>

  <script>
    // إنشاء القلوب المتطايرة
    function createFloatingHearts() {
      const heartsContainer = document.getElementById('floatingHearts');
      const hearts = ['💖', '💕', '💗', '💓', '💝', '🌹', '✨', '💫'];

      setInterval(() => {
        const heart = document.createElement('div');
        heart.className = 'heart';
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.left = Math.random() * 100 + '%';
        heart.style.animationDuration = (Math.random() * 3 + 5) + 's';
        heart.style.fontSize = (Math.random() * 10 + 15) + 'px';
        heartsContainer.appendChild(heart);

        setTimeout(() => {
          heart.remove();
        }, 8000);
      }, 3000);
    }

    // إعداد الموسيقى والتأثيرات الصوتية
    class MusicVisualizer {
      constructor() {
        this.audioContext = null;
        this.audioElement = document.getElementById('backgroundMusic');
        this.playPauseBtn = document.getElementById('playPauseBtn');
        this.muteBtn = document.getElementById('muteBtn');
        this.volumeControl = document.getElementById('volumeControl');
        this.visualizerContainer = document.getElementById('musicVisualizer');
        this.particlesContainer = document.getElementById('musicParticles');
        this.musicReactiveElements = document.querySelectorAll('.music-reactive');

        this.audioSource = null;
        this.analyser = null;
        this.gainNode = null;
        this.isPlaying = false;
        this.isMuted = false;
        this.visualizerBars = [];
        this.particles = [];
        this.dataArray = null;
        this.fallbackOscillator = null;

        this.init();
      }

      init() {
        // إنشاء عناصر المرئيات
        this.createVisualizerBars();
        this.createParticles();

        // إعداد أحداث الأزرار
        this.setupEventListeners();

        // إنشاء نغمة بديلة في حالة عدم توفر ملف الصوت
        this.createFallbackSound();
      }

      createVisualizerBars() {
        // إنشاء أشرطة المرئيات
        const barCount = window.innerWidth < 768 ? 30 : 60;

        for (let i = 0; i < barCount; i++) {
          const bar = document.createElement('div');
          bar.className = 'visualizer-bar';
          bar.style.height = '5px';
          this.visualizerContainer.appendChild(bar);
          this.visualizerBars.push(bar);
        }
      }

      createParticles() {
        // إنشاء جزيئات متحركة
        const particleCount = 30;

        for (let i = 0; i < particleCount; i++) {
          const particle = document.createElement('div');
          particle.className = 'particle';
          particle.style.left = Math.random() * 100 + '%';
          particle.style.animationDuration = (Math.random() * 5 + 5) + 's';
          particle.style.animationDelay = (Math.random() * 5) + 's';
          this.particlesContainer.appendChild(particle);
          this.particles.push(particle);
        }
      }

      setupEventListeners() {
        // زر التشغيل/الإيقاف
        this.playPauseBtn.addEventListener('click', () => {
          if (this.isPlaying) {
            this.pause();
          } else {
            this.play();
          }
        });

        // زر كتم الصوت
        this.muteBtn.addEventListener('click', () => {
          this.toggleMute();
        });

        // التحكم بمستوى الصوت
        this.volumeControl.addEventListener('input', () => {
          const volume = this.volumeControl.value / 100;
          this.setVolume(volume);
        });

        // عند انتهاء تحميل الصفحة، تشغيل الموسيقى تلقائياً
        window.addEventListener('load', () => {
          setTimeout(() => {
            this.play();
          }, 1000);
        });
      }

      createFallbackSound() {
        // إنشاء نغمة بديلة باستخدام Web Audio API
        try {
          this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
          this.gainNode = this.audioContext.createGain();
          this.analyser = this.audioContext.createAnalyser();

          this.analyser.fftSize = 256;
          this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);

          this.gainNode.connect(this.analyser);
          this.analyser.connect(this.audioContext.destination);

          // إنشاء نغمة هادئة ورومانسية
          this.createRomanticTone();
        } catch (error) {
          console.log('Web Audio API غير مدعوم، سيتم استخدام تأثيرات بديلة');
          this.startFallbackAnimation();
        }
      }

      createRomanticTone() {
        // إنشاء نغمات متعددة لخلق جو رومانسي
        const frequencies = [261.63, 329.63, 392.00, 523.25]; // C4, E4, G4, C5
        const oscillators = [];

        frequencies.forEach((freq, index) => {
          const oscillator = this.audioContext.createOscillator();
          const gainNode = this.audioContext.createGain();

          oscillator.type = 'sine';
          oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime);

          gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);

          oscillator.connect(gainNode);
          gainNode.connect(this.gainNode);

          oscillators.push({ oscillator, gainNode });
        });

        this.fallbackOscillator = oscillators;
      }

      play() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
          this.audioContext.resume();
        }

        // محاولة تشغيل الملف الصوتي أولاً
        this.audioElement.play().then(() => {
          this.setupAudioAnalysis();
        }).catch(() => {
          // في حالة فشل تشغيل الملف، استخدام النغمة البديلة
          this.playFallbackSound();
        });

        this.isPlaying = true;
        this.playPauseBtn.textContent = '⏸️';
        this.playPauseBtn.classList.add('playing');
        this.startVisualization();
      }

      pause() {
        this.audioElement.pause();

        if (this.fallbackOscillator) {
          this.fallbackOscillator.forEach(({ oscillator }) => {
            try {
              oscillator.stop();
            } catch (e) {}
          });
        }

        this.isPlaying = false;
        this.playPauseBtn.textContent = '▶️';
        this.playPauseBtn.classList.remove('playing');
      }

      playFallbackSound() {
        if (this.fallbackOscillator) {
          this.fallbackOscillator.forEach(({ oscillator }, index) => {
            oscillator.start();

            // إضافة تأثير تلاشي للنغمات
            setInterval(() => {
              const randomGain = 0.05 + Math.random() * 0.1;
              oscillator.gainNode.gain.setValueAtTime(randomGain, this.audioContext.currentTime);
            }, 2000 + index * 500);
          });
        }
      }

      setupAudioAnalysis() {
        if (!this.audioContext) return;

        if (!this.audioSource) {
          this.audioSource = this.audioContext.createMediaElementSource(this.audioElement);
          this.audioSource.connect(this.analyser);
          this.analyser.connect(this.audioContext.destination);
        }
      }

      startVisualization() {
        const animate = () => {
          if (!this.isPlaying) return;

          if (this.analyser && this.dataArray) {
            this.analyser.getByteFrequencyData(this.dataArray);
            this.updateVisualizer();
          } else {
            this.updateFallbackVisualizer();
          }

          this.updateMusicReactiveElements();
          requestAnimationFrame(animate);
        };

        animate();
      }

      updateVisualizer() {
        const barCount = this.visualizerBars.length;
        const dataStep = Math.floor(this.dataArray.length / barCount);

        for (let i = 0; i < barCount; i++) {
          const dataIndex = i * dataStep;
          const value = this.dataArray[dataIndex];
          const height = (value / 255) * 60 + 5;

          this.visualizerBars[i].style.height = height + 'px';
        }
      }

      updateFallbackVisualizer() {
        // تأثير بديل في حالة عدم وجود تحليل صوتي
        const time = Date.now() * 0.005;

        this.visualizerBars.forEach((bar, index) => {
          const height = Math.sin(time + index * 0.5) * 20 + 25;
          bar.style.height = Math.abs(height) + 'px';
        });
      }

      updateMusicReactiveElements() {
        const intensity = this.getAudioIntensity();

        this.musicReactiveElements.forEach((element, index) => {
          const scale = 1 + intensity * 0.05;
          const rotation = Math.sin(Date.now() * 0.001 + index) * intensity * 2;

          element.style.transform = `scale(${scale}) rotate(${rotation}deg)`;
        });

        // تحديث الجزيئات
        this.updateParticles(intensity);
      }

      updateParticles(intensity) {
        this.particles.forEach((particle, index) => {
          const scale = 1 + intensity * 0.1;
          const brightness = 0.5 + intensity * 0.5;

          particle.style.transform = `scale(${scale})`;
          particle.style.opacity = brightness;
        });
      }

      getAudioIntensity() {
        if (this.dataArray) {
          const sum = this.dataArray.reduce((a, b) => a + b, 0);
          return sum / (this.dataArray.length * 255);
        }

        // تأثير بديل
        return Math.abs(Math.sin(Date.now() * 0.003)) * 0.5;
      }

      setVolume(volume) {
        this.audioElement.volume = volume;

        if (this.gainNode) {
          this.gainNode.gain.setValueAtTime(volume * 0.3, this.audioContext.currentTime);
        }
      }

      toggleMute() {
        if (this.isMuted) {
          this.setVolume(this.volumeControl.value / 100);
          this.muteBtn.textContent = '🔊';
          this.isMuted = false;
        } else {
          this.setVolume(0);
          this.muteBtn.textContent = '🔇';
          this.isMuted = true;
        }
      }

      startFallbackAnimation() {
        // تأثيرات بديلة في حالة عدم دعم Web Audio API
        setInterval(() => {
          if (this.isPlaying) {
            this.updateFallbackVisualizer();
            this.updateMusicReactiveElements();
          }
        }, 50);
      }
    }

    // متغيرات عامة
    let musicVisualizer;

    // حساب "منذ لقائنا"
    const meetDate = new Date("2025-06-27T00:00:00");
    function calcSinceMeeting() {
      const now = new Date();
      let diff = now - meetDate;
      let days = Math.floor(diff / (1000 * 60 * 60 * 24));
      let months = Math.floor(days / 30.44);
      let years = Math.floor(months / 12);
      months = months % 12;
      let remainingDays = Math.floor(days - (years * 365.25) - (months * 30.44));

      let output = '';
      if (days === 0) {
        output = 'اليوم هو بداية قصتنا! 💖';
      } else if (days < 30) {
        output = `${days} يوم من الحب والسعادة ✨`;
      } else if (days < 365) {
        output = `${months} شهر و ${remainingDays} يوم من الذكريات الجميلة 💕`;
      } else {
        output = `${years} سنة و ${months} شهر و ${remainingDays} يوم من الحب الأبدي 💖`;
      }

      document.getElementById("sinceMeeting").textContent = output;
    }

    function getTimeUntil(month, day) {
      const now = new Date();
      let year = now.getFullYear();
      let target = new Date(year, month - 1, day);
      if (target < now) target.setFullYear(year + 1);

      let total = (target - now);
      let seconds = Math.floor(total / 1000);
      let minutes = Math.floor(seconds / 60);
      let hours = Math.floor(minutes / 60);
      let days = Math.floor(hours / 24);
      let months = Math.floor(days / 30.44);
      let remDays = days - months * 30.44;
      seconds = seconds % 60;
      minutes = minutes % 60;
      hours = hours % 24;

      let timeStr = '';
      if (total <= 0) {
        timeStr = 'عيد ميلاد سعيد! 🎉🎂';
      } else if (days === 0) {
        timeStr = `اليوم هو العيد! ${hours} ساعة و ${minutes} دقيقة متبقية 🎉`;
      } else if (days < 30) {
        timeStr = `${days} يوم، ${hours} ساعة، ${minutes} دقيقة، ${seconds} ثانية`;
      } else {
        timeStr = `${months} شهر و ${Math.floor(remDays)} يوم، ${hours} ساعة، ${minutes} دقيقة`;
      }
      return timeStr;
    }

    function updateCountdowns() {
      const now = new Date();
      calcSinceMeeting();
      document.getElementById("ayaCountdown").textContent = getTimeUntil(11, 8);
      document.getElementById("khalilCountdown").textContent = getTimeUntil(10, 28);
    }

    // تأثير تفاعلي للكروت
    function addCardInteractions() {
      const cards = document.querySelectorAll('.card');
      cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.2) + 's';

        card.addEventListener('mouseenter', () => {
          card.style.transform = 'translateY(-15px) scale(1.03)';
        });

        card.addEventListener('mouseleave', () => {
          card.style.transform = 'translateY(0) scale(1)';
        });
      });
    }

    // تشغيل كل شيء
    document.addEventListener('DOMContentLoaded', () => {
      // إنشاء مرئيات الموسيقى
      musicVisualizer = new MusicVisualizer();

      // تشغيل التأثيرات الأخرى
      createFloatingHearts();
      addCardInteractions();
      updateCountdowns();
      setInterval(updateCountdowns, 1000);

      // إضافة تأثيرات إضافية للقلوب المتفاعلة مع الموسيقى
      enhanceHeartsWithMusic();
    });

    // تحسين القلوب للتفاعل مع الموسيقى
    function enhanceHeartsWithMusic() {
      const originalCreateHearts = createFloatingHearts;

      // إعادة تعريف دالة إنشاء القلوب لتتفاعل مع الموسيقى
      createFloatingHearts = function() {
        const heartsContainer = document.getElementById('floatingHearts');
        const hearts = ['💖', '💕', '💗', '💓', '💝', '🌹', '✨', '💫', '🎵', '🎶'];

        setInterval(() => {
          const heart = document.createElement('div');
          heart.className = 'heart music-reactive';
          heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
          heart.style.left = Math.random() * 100 + '%';

          // تأثيرات متفاعلة مع الموسيقى
          const baseDuration = Math.random() * 3 + 5;
          const musicIntensity = musicVisualizer ? musicVisualizer.getAudioIntensity() : 0.5;
          const adjustedDuration = baseDuration * (1 - musicIntensity * 0.3);

          heart.style.animationDuration = adjustedDuration + 's';
          heart.style.fontSize = (Math.random() * 10 + 15 + musicIntensity * 10) + 'px';

          // ألوان متغيرة حسب الموسيقى
          const hue = Math.random() * 360;
          const saturation = 70 + musicIntensity * 30;
          const lightness = 60 + musicIntensity * 20;
          heart.style.color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
          heart.style.textShadow = `0 0 ${5 + musicIntensity * 10}px currentColor`;

          heartsContainer.appendChild(heart);

          setTimeout(() => {
            heart.remove();
          }, adjustedDuration * 1000);
        }, 2000 - (musicVisualizer ? musicVisualizer.getAudioIntensity() * 1000 : 0));
      };

      // تشغيل النسخة المحسنة
      createFloatingHearts();
    }

    // إضافة تأثيرات تفاعلية للخلفية
    function addBackgroundMusicEffects() {
      const body = document.body;

      setInterval(() => {
        if (musicVisualizer && musicVisualizer.isPlaying) {
          const intensity = musicVisualizer.getAudioIntensity();

          // تغيير شدة الخلفية حسب الموسيقى
          const brightness = 1 + intensity * 0.2;
          body.style.filter = `brightness(${brightness}) saturate(${1 + intensity * 0.3})`;

          // تأثير نبض خفيف للخلفية
          const scale = 1 + intensity * 0.02;
          body.style.transform = `scale(${scale})`;
        }
      }, 100);
    }

    // تشغيل تأثيرات الخلفية
    setTimeout(addBackgroundMusicEffects, 2000);
  </script>
</body>
</html>