<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="موقع رومانسي للخليل وآيات - قصة حب جميلة">
  <meta name="keywords" content="حب, رومانسية, الخليل, آيات">
  <meta name="author" content="الخليل وآيات">
  
  <title>الخليل وآيات 💖 - قصة حب جميلة</title>
  
  <!-- تحميل الخطوط مع تحسين الأداء -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
  
  <!-- ربط ملف CSS -->
  <link rel="stylesheet" href="styles.css">
  
  <!-- تحسين SEO -->
  <meta property="og:title" content="الخليل وآيات 💖">
  <meta property="og:description" content="قصة حب جميلة">
  <meta property="og:type" content="website">
  
  <!-- أيقونة الموقع -->
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💖</text></svg>">
</head>

<body>
  <!-- عناصر التأثيرات البصرية الجميلة -->
  <div class="floating-hearts" id="floatingHearts" aria-hidden="true"></div>
  <div class="music-particles" id="musicParticles" aria-hidden="true"></div>
  
  <!-- حلقات النبض -->
  <div class="pulse-ring" aria-hidden="true"></div>
  <div class="pulse-ring" aria-hidden="true"></div>
  <div class="pulse-ring" aria-hidden="true"></div>

  <!-- المحتوى الرئيسي -->
  <header>
    <h1>💖 الخليل وآيات 💖</h1>
  </header>

  <main class="container">
    <section class="card" style="animation-delay: 0.2s;" aria-labelledby="meeting-title">
      <h2 id="meeting-title">✨ منذ لقائنا الأول</h2>
      <div class="note">27 يونيو 2025</div>
      <div id="sinceMeeting" class="big" role="timer" aria-live="polite"></div>
    </section>

    <section class="card" style="animation-delay: 0.4s;" aria-labelledby="aya-birthday">
      <h2 id="aya-birthday">🎂 عيد ميلاد آيات الحبيبة</h2>
      <div class="note">8 نوفمبر 1999</div>
      <div id="ayaCountdown" class="big" role="timer" aria-live="polite"></div>
    </section>

    <section class="card" style="animation-delay: 0.6s;" aria-labelledby="khalil-birthday">
      <h2 id="khalil-birthday">🎉 عيد ميلاد الخليل العزيز</h2>
      <div class="note">28 أكتوبر 1999</div>
      <div id="khalilCountdown" class="big" role="timer" aria-live="polite"></div>
    </section>

    <section class="card special-card" style="animation-delay: 0.8s;" aria-labelledby="beautiful-fact">
      <h2 id="beautiful-fact">💫 حقيقة جميلة</h2>
      <div class="big">الخليل أكبر من آيات بـ 11 يوم فقط 👑💕</div>
      <div class="note">فارق عمر رومانسي وجميل</div>
    </section>

    <section class="card promise-card" style="animation-delay: 1s;" aria-labelledby="eternal-love">
      <h2 id="eternal-love">💍 عهد الحب الأبدي</h2>
      <div class="big">معاً نحو المستقبل... الزواج والسعادة والحب الصادق 💖</div>
      <div class="note">وعد بالحب والاحترام والإخلاص</div>
    </section>

    <section class="card" style="animation-delay: 1.2s;" aria-labelledby="love-message">
      <h2 id="love-message">🌟 رسالة حب</h2>
      <div class="big">كل لحظة معك هي ذكرى جميلة... وكل يوم قادم هو حلم نحققه سوياً 💕</div>
    </section>
  </main>

  <!-- ربط ملف JavaScript -->
  <script src="script.js"></script>
  
  <!-- تحسين الأداء -->
  <script>
    // تحسين تحميل الخطوط
    if ('fonts' in document) {
      document.fonts.ready.then(() => {
        document.body.classList.add('fonts-loaded');
      });
    }
    
    // تحسين الأداء للأجهزة الضعيفة
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) {
      document.body.classList.add('low-performance');
    }
    
    // تقليل الحركة للمستخدمين الذين يفضلون ذلك
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      document.body.classList.add('reduced-motion');
    }
  </script>
</body>
</html>
