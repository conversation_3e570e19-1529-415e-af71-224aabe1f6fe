<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>الخليل وآيات 💖</title>
  <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
  <style>
    /* خطوط احتياطية في حالة فشل تحميل الخطوط الخارجية */
    @font-face {
      font-family: 'Cairo-Fallback';
      src: local('Tahoma'), local('Arial Unicode MS'), local('Arial');
      font-display: swap;
    }
    @font-face {
      font-family: 'Amiri-Fallback';
      src: local('Times New Roman'), local('Georgia'), local('serif');
      font-display: swap;
    }
  </style>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Cairo', 'Cairo-Fallback', 'Tahoma', 'Arial', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
      color: #333;
      text-align: center;
      padding: 3rem 2rem;
      min-height: 100vh;
      position: relative;
      overflow-x: hidden;
    }

    /* الخلفية الجميلة مع القلوب والموجات */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.1)">💖</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>'),
        radial-gradient(circle at 20% 80%, rgba(255,107,107,0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(254,202,87,0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(72,219,251,0.15) 0%, transparent 50%),
        radial-gradient(circle at 60% 60%, rgba(255,159,243,0.15) 0%, transparent 50%);
      animation: backgroundWaves 20s ease infinite;
      pointer-events: none;
      z-index: -2;
    }

    body::after {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 70% 30%, rgba(255,107,107,0.1) 0%, transparent 60%),
        radial-gradient(circle at 30% 70%, rgba(254,202,87,0.1) 0%, transparent 60%),
        radial-gradient(circle at 90% 90%, rgba(72,219,251,0.1) 0%, transparent 60%),
        radial-gradient(circle at 10% 10%, rgba(255,159,243,0.1) 0%, transparent 60%);
      animation: backgroundWaves 25s ease infinite reverse;
      pointer-events: none;
      z-index: -2;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    @keyframes backgroundWaves {
      0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.4;
      }
      25% {
        transform: scale(1.1) rotate(90deg);
        opacity: 0.6;
      }
      50% {
        transform: scale(0.9) rotate(180deg);
        opacity: 0.5;
      }
      75% {
        transform: scale(1.05) rotate(270deg);
        opacity: 0.7;
      }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    h1 {
      font-family: 'Amiri', 'Amiri-Fallback', 'Times New Roman', serif;
      font-size: 3.5rem;
      font-weight: 700;
      color: #fff;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      margin-bottom: 2rem;
      background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradientShift 8s ease infinite;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2.5rem;
      padding: 2rem;
      width: 100%;
      box-sizing: border-box;
    }

    .card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 25px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      padding: 2.5rem 2rem;
      margin: 2rem 0;
      position: relative;
      overflow: hidden;
      transition: all 0.4s ease;
      animation: fadeInUp 0.8s ease forwards;
      border: 1px solid rgba(255,255,255,0.3);
      z-index: 2;
      width: 100%;
      box-sizing: border-box;
    }



    .card:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 30px 60px rgba(0,0,0,0.15);
    }

    .card:hover::before {
      opacity: 1;
    }

    .card h2 {
      font-family: 'Amiri', 'Amiri-Fallback', 'Times New Roman', serif;
      font-size: 1.8rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 1.5rem;
      position: relative;
    }

    .card h2::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      border-radius: 2px;
    }

    .big {
      font-size: 1.8rem;
      font-weight: 600;
      background: linear-gradient(45deg, #e74c3c, #f39c12, #9b59b6, #3498db);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradientShift 6s ease infinite;
      line-height: 1.4;
      min-height: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .note {
      color: #7f8c8d;
      font-style: italic;
      margin-top: 15px;
      font-size: 1.1rem;
    }

    .special-card {
      background: linear-gradient(135deg, rgba(255,107,107,0.1), rgba(254,202,87,0.1));
      border: 2px solid rgba(255,107,107,0.3);
    }

    .promise-card {
      background: linear-gradient(135deg, rgba(155,89,182,0.1), rgba(52,152,219,0.1));
      border: 2px solid rgba(155,89,182,0.3);
    }



    /* القلوب والجزيئات المتطايرة */
    .floating-hearts {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .heart {
      position: absolute;
      font-size: 28px;
      color: rgba(255,255,255,0.6);
      animation: floatUp 8s linear infinite;
      transition: all 0.3s ease;
      text-shadow: 0 0 10px currentColor;
    }

    @keyframes floatUp {
      0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 1;
      }
      100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
      }
    }

    .music-particles {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .particle {
      position: absolute;
      width: 6px;
      height: 6px;
      background: radial-gradient(circle, rgba(255,107,107,0.9), transparent);
      border-radius: 50%;
      animation: particle-float 8s linear infinite;
      box-shadow: 0 0 8px currentColor;
    }

    .particle:nth-child(2n) {
      background: radial-gradient(circle, rgba(254,202,87,0.9), transparent);
      animation-duration: 10s;
      width: 5px;
      height: 5px;
    }

    .particle:nth-child(3n) {
      background: radial-gradient(circle, rgba(72,219,251,0.9), transparent);
      animation-duration: 12s;
      width: 7px;
      height: 7px;
    }

    .particle:nth-child(4n) {
      background: radial-gradient(circle, rgba(255,159,243,0.9), transparent);
      animation-duration: 6s;
      width: 5px;
      height: 5px;
    }

    .particle:nth-child(5n) {
      background: radial-gradient(circle, rgba(155,89,182,0.9), transparent);
      animation-duration: 9s;
      width: 6px;
      height: 6px;
    }

    @keyframes particle-float {
      0% {
        transform: translateY(100vh) translateX(0) scale(0) rotate(0deg);
        opacity: 0;
      }
      10% {
        opacity: 1;
        transform: scale(1);
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateY(-100px) translateX(50px) scale(0) rotate(360deg);
        opacity: 0;
      }
    }

    /* حلقات النبض الجميلة */
    .pulse-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 200px;
      border: 2px solid rgba(255,255,255,0.2);
      border-radius: 50%;
      animation: pulse-ring 4s ease-in-out infinite;
      pointer-events: none;
      z-index: -1;
    }

    .pulse-ring:nth-child(2) {
      width: 300px;
      height: 300px;
      animation-delay: 1s;
      opacity: 0.5;
    }

    .pulse-ring:nth-child(3) {
      width: 400px;
      height: 400px;
      animation-delay: 2s;
      opacity: 0.3;
    }

    @keyframes pulse-ring {
      0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
      }
      50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.3;
      }
      100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
      }
    }

    /* تصميم متجاوب محسن */
    @media (max-width: 1200px) {
      .container {
        max-width: 100%;
        padding: 1.5rem;
      }

      .card {
        margin: 1.5rem 0;
      }
    }

    @media (max-width: 768px) {
      body {
        padding: 2rem 1rem;
      }

      h1 {
        font-size: 2.5rem;
        margin-bottom: 2rem;
      }

      .container {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 1rem;
      }

      .card {
        padding: 2rem 1.5rem;
        margin: 1rem 0;
        border-radius: 20px;
      }

      .card h2 {
        font-size: 1.6rem;
      }

      .big {
        font-size: 1.4rem;
        line-height: 1.5;
        min-height: 3rem;
        padding: 0.5rem 0;
      }

      .note {
        font-size: 1rem;
      }



      /* تحسين الإيموجي للشاشات الصغيرة */
      .heart {
        font-size: 22px;
      }

      .particle {
        width: 4px;
        height: 4px;
      }
    }

    @media (max-width: 480px) {
      body {
        padding: 1.5rem 0.5rem;
      }

      h1 {
        font-size: 2rem;
        margin-bottom: 1.5rem;
      }

      .container {
        gap: 1.5rem;
        padding: 0.5rem;
      }

      .card {
        padding: 1.5rem 1rem;
        border-radius: 18px;
        margin: 0.8rem 0;
      }

      .card h2 {
        font-size: 1.4rem;
        margin-bottom: 1rem;
      }

      .big {
        font-size: 1.2rem;
        line-height: 1.4;
        min-height: 2.8rem;
        padding: 0.3rem 0;
      }

      .note {
        font-size: 0.9rem;
        margin-top: 10px;
      }



      /* تقليل الإيموجي للشاشات الصغيرة جداً */
      .heart {
        font-size: 18px;
      }

      .particle {
        width: 3px;
        height: 3px;
      }
    }

    @media (max-width: 320px) {
      body {
        padding: 1rem 0.3rem;
      }

      h1 {
        font-size: 1.8rem;
        margin-bottom: 1rem;
      }

      .container {
        padding: 0.3rem;
        gap: 1rem;
      }

      .card {
        padding: 1.2rem 0.8rem;
        margin: 0.5rem 0;
      }

      .card h2 {
        font-size: 1.2rem;
      }

      .big {
        font-size: 1.1rem;
        min-height: 2.5rem;
        padding: 0.2rem 0;
      }

      .note {
        font-size: 0.8rem;
      }
    }

    /* تحسين للشاشات الكبيرة */
    @media (min-width: 1400px) {
      .container {
        max-width: 1400px;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      }

      h1 {
        font-size: 4rem;
      }

      .card {
        padding: 3rem 2.5rem;
      }

      .big {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <!-- عناصر التأثيرات البصرية الجميلة -->
  <div class="floating-hearts" id="floatingHearts"></div>
  <div class="music-particles" id="musicParticles"></div>

  <!-- حلقات النبض -->
  <div class="pulse-ring"></div>
  <div class="pulse-ring"></div>
  <div class="pulse-ring"></div>



  <h1>💖 الخليل وآيات 💖</h1>

  <div class="container">
    <div class="card" style="animation-delay: 0.2s;">
      <h2>✨ منذ لقائنا الأول</h2>
      <div class="note">27 يونيو 2024</div>
      <div id="sinceMeeting" class="big"></div>
    </div>

    <div class="card" style="animation-delay: 0.4s;">
      <h2>🎂 عيد ميلاد آيات الحبيبة</h2>
      <div class="note">8 نوفمبر 1999</div>
      <div id="ayaCountdown" class="big"></div>
    </div>

    <div class="card" style="animation-delay: 0.6s;">
      <h2>🎉 عيد ميلاد الخليل العزيز</h2>
      <div class="note">28 أكتوبر 1999</div>
      <div id="khalilCountdown" class="big"></div>
    </div>

    <div class="card special-card" style="animation-delay: 0.8s;">
      <h2>💫 حقيقة جميلة</h2>
      <div class="big">الخليل أكبر من آيات بـ 11 يوم فقط 👑💕</div>
      <div class="note">فارق عمر رومانسي وجميل</div>
    </div>

    <div class="card promise-card" style="animation-delay: 1s;">
      <h2>💍 عهد الحب الأبدي</h2>
      <div class="big">معاً نحو المستقبل... الزواج والسعادة والحب الصادق 💖</div>
      <div class="note">وعد بالحب والاحترام والإخلاص</div>
    </div>

    <div class="card" style="animation-delay: 1.2s;">
      <h2>🌟 رسالة حب</h2>
      <div class="big">كل لحظة معك هي ذكرى جميلة... وكل يوم قادم هو حلم نحققه سوياً 💕</div>
    </div>
  </div>
  <script>


    // حساب "منذ لقائنا" - تاريخ صحيح في الماضي
    const meetDate = new Date("2024-06-27T00:00:00");

    // دالة مساعدة لتحسين عرض الأرقام
    function formatNumber(num) {
      return num.toLocaleString('ar-EG');
    }

    // دالة مساعدة لاختيار الكلمة المناسبة حسب العدد
    function getWordForm(number, singular, dual, plural) {
      if (number === 1) return singular;
      if (number === 2) return dual;
      if (number >= 3 && number <= 10) return plural;
      return plural;
    }
    function calcSinceMeeting() {
      try {
        const now = new Date();
        let diff = now - meetDate;

        // التأكد من أن التاريخ صحيح
        if (diff < 0) {
          document.getElementById("sinceMeeting").textContent = 'قريباً... 💖';
          return;
        }

        let totalDays = Math.floor(diff / (1000 * 60 * 60 * 24));

        let output = '';

        if (totalDays === 0) {
          output = 'اليوم هو بداية قصتنا! 💖';
        } else if (totalDays < 30) {
          // أقل من 30 يوم - عرض الأيام فقط
          const dayWord = getWordForm(totalDays, 'يوم واحد', 'يومان', 'أيام');
          output = `${formatNumber(totalDays)} ${dayWord} من الحب والسعادة ✨`;
        } else if (totalDays < 365) {
          // من 30 يوم إلى سنة - عرض الأشهر والأيام
          let months = Math.floor(totalDays / 30.44);
          let remainingDays = Math.floor(totalDays - (months * 30.44));

          const monthWord = getWordForm(months, 'شهر واحد', 'شهران', 'أشهر');

          if (remainingDays === 0) {
            output = `${formatNumber(months)} ${monthWord} من الذكريات الجميلة 💕`;
          } else {
            const dayWord = getWordForm(remainingDays, 'يوم واحد', 'يومان', 'أيام');
            output = `${formatNumber(months)} ${monthWord} و ${formatNumber(remainingDays)} ${dayWord} من الذكريات الجميلة 💕`;
          }
        } else {
          // أكثر من سنة - عرض السنوات والأشهر والأيام
          let years = Math.floor(totalDays / 365.25);
          let remainingDaysAfterYears = totalDays - (years * 365.25);
          let months = Math.floor(remainingDaysAfterYears / 30.44);
          let days = Math.floor(remainingDaysAfterYears - (months * 30.44));

          const yearWord = getWordForm(years, 'سنة واحدة', 'سنتان', 'سنوات');

          if (months === 0 && days === 0) {
            output = `${formatNumber(years)} ${yearWord} من الحب الأبدي 💖`;
          } else if (days === 0) {
            const monthWord = getWordForm(months, 'شهر واحد', 'شهران', 'أشهر');
            output = `${formatNumber(years)} ${yearWord} و ${formatNumber(months)} ${monthWord} من الحب الأبدي 💖`;
          } else if (months === 0) {
            const dayWord = getWordForm(days, 'يوم واحد', 'يومان', 'أيام');
            output = `${formatNumber(years)} ${yearWord} و ${formatNumber(days)} ${dayWord} من الحب الأبدي 💖`;
          } else {
            const monthWord = getWordForm(months, 'شهر واحد', 'شهران', 'أشهر');
            const dayWord = getWordForm(days, 'يوم واحد', 'يومان', 'أيام');
            output = `${formatNumber(years)} ${yearWord} و ${formatNumber(months)} ${monthWord} و ${formatNumber(days)} ${dayWord} من الحب الأبدي 💖`;
          }
        }

        const element = document.getElementById("sinceMeeting");
        if (element) {
          element.textContent = output;
        }
      } catch (error) {
        console.log('خطأ في حساب التاريخ:', error);
        const element = document.getElementById("sinceMeeting");
        if (element) {
          element.textContent = 'قصة حب جميلة 💖';
        }
      }
    }

    function getTimeUntil(month, day) {
      try {
        const now = new Date();
        let year = now.getFullYear();
        let target = new Date(year, month - 1, day);

        // إذا كان التاريخ قد مر هذا العام، انتقل للعام القادم
        if (target < now) target.setFullYear(year + 1);

        let total = (target - now);

        // التحقق من صحة التاريخ
        if (isNaN(total) || total < 0) {
          return 'تاريخ غير صحيح 📅';
        }

        let seconds = Math.floor(total / 1000);
        let minutes = Math.floor(seconds / 60);
        let hours = Math.floor(minutes / 60);
        let totalDays = Math.floor(hours / 24);

        seconds = seconds % 60;
        minutes = minutes % 60;
        hours = hours % 24;

        let timeStr = '';

        if (total <= 86400000) { // أقل من يوم واحد
          if (hours === 0 && minutes === 0) {
            timeStr = `عيد ميلاد سعيد! 🎉🎂`;
          } else if (hours === 0) {
            const minuteWord = getWordForm(minutes, 'دقيقة واحدة', 'دقيقتان', 'دقائق');
            timeStr = `اليوم هو العيد! ${formatNumber(minutes)} ${minuteWord} متبقية 🎉`;
          } else {
            const hourWord = getWordForm(hours, 'ساعة واحدة', 'ساعتان', 'ساعات');
            const minuteWord = getWordForm(minutes, 'دقيقة', 'دقيقتان', 'دقائق');
            timeStr = `اليوم هو العيد! ${formatNumber(hours)} ${hourWord} و ${formatNumber(minutes)} ${minuteWord} متبقية 🎉`;
          }
        } else if (totalDays < 30) {
          // أقل من 30 يوم - عرض الأيام والساعات والدقائق والثواني فقط (بدون أشهر)
          const dayWord = getWordForm(totalDays, 'يوم واحد', 'يومان', 'أيام');
          const hourWord = getWordForm(hours, 'ساعة', 'ساعتان', 'ساعات');
          const minuteWord = getWordForm(minutes, 'دقيقة', 'دقيقتان', 'دقائق');
          const secondWord = getWordForm(seconds, 'ثانية', 'ثانيتان', 'ثوان');

          timeStr = `${formatNumber(totalDays)} ${dayWord}، ${formatNumber(hours)} ${hourWord}، ${formatNumber(minutes)} ${minuteWord}، ${formatNumber(seconds)} ${secondWord}`;
        } else {
          // 30 يوم أو أكثر - عرض الأشهر والأيام والساعات والدقائق (إخفاء الثواني)
          let months = Math.floor(totalDays / 30.44);
          let remainingDays = Math.floor(totalDays - (months * 30.44));

          const monthWord = getWordForm(months, 'شهر واحد', 'شهران', 'أشهر');
          const hourWord = getWordForm(hours, 'ساعة', 'ساعتان', 'ساعات');
          const minuteWord = getWordForm(minutes, 'دقيقة', 'دقيقتان', 'دقائق');

          if (remainingDays === 0) {
            timeStr = `${formatNumber(months)} ${monthWord}، ${formatNumber(hours)} ${hourWord}، ${formatNumber(minutes)} ${minuteWord}`;
          } else {
            const dayWord = getWordForm(remainingDays, 'يوم واحد', 'يومان', 'أيام');
            timeStr = `${formatNumber(months)} ${monthWord} و ${formatNumber(remainingDays)} ${dayWord}، ${formatNumber(hours)} ${hourWord}، ${formatNumber(minutes)} ${minuteWord}`;
          }
        }

        return timeStr;
      } catch (error) {
        console.log('خطأ في حساب العد التنازلي:', error);
        return 'قريباً... 🎂';
      }
    }

    function updateCountdowns() {
      try {
        calcSinceMeeting();

        const ayaElement = document.getElementById("ayaCountdown");
        const khalilElement = document.getElementById("khalilCountdown");

        if (ayaElement) {
          ayaElement.textContent = getTimeUntil(11, 8);
        }

        if (khalilElement) {
          khalilElement.textContent = getTimeUntil(10, 28);
        }
      } catch (error) {
        console.log('خطأ في تحديث العدادات:', error);
      }
    }



    // إنشاء القلوب المتطايرة - محسن للأداء
    function createFloatingHearts() {
      const heartsContainer = document.getElementById('floatingHearts');
      if (!heartsContainer) return;

      // تقليل عدد الإيموجي لتحسين الأداء
      const hearts = ['💖', '💕', '💗', '💓', '💝', '🌹', '✨', '💫'];
      let heartCount = 0;
      const maxHearts = 15; // حد أقصى للقلوب في الشاشة

      // تحديد العدد والحجم حسب حجم الشاشة
      const screenWidth = window.innerWidth;
      let heartsPerInterval, minSize, maxSize, interval;

      if (screenWidth <= 480) {
        heartsPerInterval = 1;
        minSize = 18;
        maxSize = 25;
        interval = 3000; // تقليل التردد
      } else if (screenWidth <= 768) {
        heartsPerInterval = 1;
        minSize = 22;
        maxSize = 30;
        interval = 2500;
      } else {
        heartsPerInterval = 2;
        minSize = 25;
        maxSize = 35; // تقليل الحد الأقصى
        interval = 2000;
      }

      const heartInterval = setInterval(() => {
        // تنظيف القلوب الزائدة
        if (heartCount >= maxHearts) {
          const oldHearts = heartsContainer.querySelectorAll('.heart');
          if (oldHearts.length > 0) {
            oldHearts[0].remove();
            heartCount--;
          }
        }

        for (let i = 0; i < heartsPerInterval && heartCount < maxHearts; i++) {
          try {
            const heart = document.createElement('div');
            heart.className = 'heart';
            heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
            heart.style.left = Math.random() * 100 + '%';
            heart.style.animationDuration = (Math.random() * 3 + 5) + 's';

            const fontSize = Math.random() * (maxSize - minSize) + minSize;
            heart.style.fontSize = fontSize + 'px';

            const hue = Math.random() * 360;
            heart.style.color = `hsl(${hue}, 70%, 70%)`;
            heart.style.textShadow = `0 0 8px currentColor`;

            heartsContainer.appendChild(heart);
            heartCount++;

            // إزالة القلب بعد انتهاء الأنيميشن
            setTimeout(() => {
              if (heart.parentNode) {
                heart.remove();
                heartCount--;
              }
            }, parseInt(heart.style.animationDuration) * 1000);
          } catch (error) {
            console.log('خطأ في إنشاء القلب:', error);
          }
        }
      }, interval);

      // تنظيف عند إغلاق الصفحة
      window.addEventListener('beforeunload', () => {
        clearInterval(heartInterval);
      });
    }

    // إنشاء الجزيئات المتطايرة - محسن للأداء
    function createParticles() {
      const particlesContainer = document.getElementById('musicParticles');
      if (!particlesContainer) return;

      // تقليل العدد لتحسين الأداء
      const particleCount = window.innerWidth <= 768 ? 15 : 25;
      const maxParticles = window.innerWidth <= 768 ? 20 : 30;

      for (let i = 0; i < particleCount; i++) {
        try {
          const particle = document.createElement('div');
          particle.className = 'particle';
          particle.style.left = Math.random() * 100 + '%';
          particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
          particle.style.animationDelay = (Math.random() * 5) + 's';
          particle.style.opacity = Math.random() * 0.4 + 0.4;

          particlesContainer.appendChild(particle);
        } catch (error) {
          console.log('خطأ في إنشاء الجزيء:', error);
        }
      }

      // إضافة جزيئات بشكل محدود
      const particleInterval = setInterval(() => {
        if (particlesContainer.children.length < maxParticles) {
          try {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
            particle.style.animationDelay = '0s';
            particle.style.opacity = Math.random() * 0.4 + 0.4;

            particlesContainer.appendChild(particle);

            // إزالة الجزيء بعد انتهاء الأنيميشن
            setTimeout(() => {
              if (particle.parentNode) {
                particle.remove();
              }
            }, parseInt(particle.style.animationDuration) * 1000);
          } catch (error) {
            console.log('خطأ في إنشاء جزيء إضافي:', error);
          }
        }
      }, 5000); // تقليل التردد إلى كل 5 ثوان

      // تنظيف عند إغلاق الصفحة
      window.addEventListener('beforeunload', () => {
        clearInterval(particleInterval);
      });
    }

    // تشغيل تلقائي عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', () => {
      // تشغيل التأثيرات البصرية
      createFloatingHearts();
      createParticles();

      updateCountdowns();
      setInterval(updateCountdowns, 1000);


    });

    // تحسين الأداء وإدارة الذاكرة
    function cleanupResources() {
      try {
        // إيقاف جميع الفواصل الزمنية
        const intervals = window.setInterval(() => {}, 1000);
        for (let i = 1; i <= intervals; i++) {
          window.clearInterval(i);
        }



        // تنظيف العناصر المتحركة
        const heartsContainer = document.getElementById('floatingHearts');
        const particlesContainer = document.getElementById('musicParticles');

        if (heartsContainer) {
          heartsContainer.innerHTML = '';
        }

        if (particlesContainer) {
          particlesContainer.innerHTML = '';
        }

        console.log('تم تنظيف الموارد بنجاح');
      } catch (error) {
        console.log('خطأ في تنظيف الموارد:', error);
      }
    }

    // تنظيف عند إغلاق الصفحة
    window.addEventListener('beforeunload', cleanupResources);
    window.addEventListener('pagehide', cleanupResources);


  </script>
</body>
</html>
