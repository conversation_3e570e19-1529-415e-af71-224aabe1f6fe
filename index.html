<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>الخليل وآيات 💖</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      background: linear-gradient(to top left, #ffe9f2, #ffffff);
      color: #333;
      text-align: center;
      padding: 2rem;
    }
    h1 { color: #d63384; }
    .card {
      background: #fff;
      border-radius: 20px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.1);
      padding: 20px;
      margin: 20px auto;
      max-width: 500px;
    }
    .big { font-size: 1.5rem; color: #e83e8c; }
    .note { color: #6c757d; font-style: italic; margin-top: 10px; }
  </style>
</head>
<body>

  <h1>💖 الخليل وآيات 💖</h1>

  <div class="card">
    <h2>منذ لقائنا الأول (27-06-2025)</h2>
    <div id="sinceMeeting" class="big"></div>
  </div>

  <div class="card">
    <h2>🎂 عيد ميلاد آيات (08-11-1999)</h2>
    <div id="ayaCountdown" class="big"></div>
  </div>

  <div class="card">
    <h2>🎉 عيد ميلاد الخليل (28-10-1999)</h2>
    <div id="khalilCountdown" class="big"></div>
  </div>

  <div class="card">
    <h2>📌 ملاحظة</h2>
    <div class="big">الخليل أكبر من آيات بـ 11 يوم 🧓👧</div>
  </div>

  <div class="card">
    <h2>💍 عهدنا</h2>
    <div class="big">الزواج ولمّ الشمل على الحب والاحترام</div>
  </div>

  <script>
    const now = new Date();

    // حساب "منذ لقائنا"
    const meetDate = new Date("2025-06-27T00:00:00");
    function calcSinceMeeting() {
      let diff = now - meetDate;
      let days = Math.floor(diff / (1000 * 60 * 60 * 24));
      let months = Math.floor(days / 30.44);
      let years = Math.floor(months / 12);
      months = months % 12;
      let remainingDays = Math.floor(days - (years * 365.25) - (months * 30.44));

      let output = `${days} يوم`;
      if (days >= 30) output = `${months} شهر و ${remainingDays} يوم`;
      if (days >= 365) output = `${years} سنة و ${months} شهر و ${remainingDays} يوم`;
      document.getElementById("sinceMeeting").textContent = output;
    }

    function getTimeUntil(month, day) {
      const now = new Date();
      let year = now.getFullYear();
      let target = new Date(year, month - 1, day);
      if (target < now) target.setFullYear(year + 1);

      let total = (target - now);
      let seconds = Math.floor(total / 1000);
      let minutes = Math.floor(seconds / 60);
      let hours = Math.floor(minutes / 60);
      let days = Math.floor(hours / 24);
      let months = Math.floor(days / 30.44);
      let remDays = days - months * 30.44;
      seconds = seconds % 60;
      minutes = minutes % 60;
      hours = hours % 24;

      let timeStr = ``;
      if (days < 30) {
        timeStr = `${days} يوم، ${hours} ساعة، ${minutes} دقيقة، ${seconds} ثانية`;
      } else {
        timeStr = `${months} شهر و ${Math.floor(remDays)} يوم، ${hours} ساعة، ${minutes} دقيقة، ${seconds} ثانية`;
      }
      return timeStr;
    }

    function updateCountdowns() {
      document.getElementById("ayaCountdown").textContent = getTimeUntil(11, 8);
      document.getElementById("khalilCountdown").textContent = getTimeUntil(10, 28);
    }

    calcSinceMeeting();
    updateCountdowns();
    setInterval(() => {
      updateCountdowns();
    }, 1000);
  </script>
</body>
</html>