<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>الخليل وآيات 💖</title>
  <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Cairo', 'Arial', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
      color: #333;
      text-align: center;
      padding: 2rem 1rem;
      min-height: 100vh;
      position: relative;
      overflow-x: hidden;
    }

    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.1)">💖</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
      pointer-events: none;
      z-index: -1;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    h1 {
      font-family: 'Amiri', serif;
      font-size: 3.5rem;
      font-weight: 700;
      color: #fff;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      margin-bottom: 2rem;
      animation: float 3s ease-in-out infinite;
      background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: float 3s ease-in-out infinite, gradientShift 8s ease infinite;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      padding: 0 1rem;
    }

    .card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 25px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2);
      padding: 2.5rem 2rem;
      margin: 1rem 0;
      position: relative;
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      animation: fadeInUp 0.8s ease forwards;
      border: 1px solid rgba(255,255,255,0.3);
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
      transition: left 0.5s;
    }

    .card:hover::before {
      left: 100%;
    }

    .card:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 30px 60px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.3);
    }

    .card h2 {
      font-family: 'Amiri', serif;
      font-size: 1.8rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 1.5rem;
      position: relative;
    }

    .card h2::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      border-radius: 2px;
    }

    .big {
      font-size: 1.8rem;
      font-weight: 600;
      background: linear-gradient(45deg, #e74c3c, #f39c12, #9b59b6, #3498db);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradientShift 6s ease infinite;
      line-height: 1.4;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .note {
      color: #7f8c8d;
      font-style: italic;
      margin-top: 15px;
      font-size: 1.1rem;
    }

    .special-card {
      background: linear-gradient(135deg, rgba(255,107,107,0.1), rgba(254,202,87,0.1));
      border: 2px solid rgba(255,107,107,0.3);
    }

    .promise-card {
      background: linear-gradient(135deg, rgba(155,89,182,0.1), rgba(52,152,219,0.1));
      border: 2px solid rgba(155,89,182,0.3);
    }

    @media (max-width: 768px) {
      h1 {
        font-size: 2.5rem;
      }

      .container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .card {
        padding: 2rem 1.5rem;
      }

      .big {
        font-size: 1.5rem;
      }
    }

    .floating-hearts {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .heart {
      position: absolute;
      font-size: 20px;
      color: rgba(255,255,255,0.3);
      animation: floatUp 8s linear infinite;
    }

    @keyframes floatUp {
      0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 1;
      }
      100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
      }
    }
  </style>
</head>
<body>
  <div class="floating-hearts" id="floatingHearts"></div>

  <h1>💖 الخليل وآيات 💖</h1>

  <div class="container">
    <div class="card" style="animation-delay: 0.2s;">
      <h2>✨ منذ لقائنا الأول</h2>
      <div class="note">27 يونيو 2025</div>
      <div id="sinceMeeting" class="big"></div>
    </div>

    <div class="card" style="animation-delay: 0.4s;">
      <h2>🎂 عيد ميلاد آيات الحبيبة</h2>
      <div class="note">8 نوفمبر 1999</div>
      <div id="ayaCountdown" class="big"></div>
    </div>

    <div class="card" style="animation-delay: 0.6s;">
      <h2>🎉 عيد ميلاد الخليل العزيز</h2>
      <div class="note">28 أكتوبر 1999</div>
      <div id="khalilCountdown" class="big"></div>
    </div>

    <div class="card special-card" style="animation-delay: 0.8s;">
      <h2>� حقيقة جميلة</h2>
      <div class="big">الخليل أكبر من آيات بـ 11 يوم فقط 👑�</div>
      <div class="note">فارق عمر رومانسي وجميل</div>
    </div>

    <div class="card promise-card" style="animation-delay: 1s;">
      <h2>💍 عهد الحب الأبدي</h2>
      <div class="big">معاً نحو المستقبل... الزواج والسعادة والحب الصادق 💖</div>
      <div class="note">وعد بالحب والاحترام والإخلاص</div>
    </div>

    <div class="card" style="animation-delay: 1.2s;">
      <h2>🌟 رسالة حب</h2>
      <div class="big">كل لحظة معك هي ذكرى جميلة... وكل يوم قادم هو حلم نحققه سوياً 💕</div>
    </div>
  </div>

  <script>
    // إنشاء القلوب المتطايرة
    function createFloatingHearts() {
      const heartsContainer = document.getElementById('floatingHearts');
      const hearts = ['💖', '💕', '💗', '💓', '💝', '🌹', '✨', '💫'];

      setInterval(() => {
        const heart = document.createElement('div');
        heart.className = 'heart';
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.left = Math.random() * 100 + '%';
        heart.style.animationDuration = (Math.random() * 3 + 5) + 's';
        heart.style.fontSize = (Math.random() * 10 + 15) + 'px';
        heartsContainer.appendChild(heart);

        setTimeout(() => {
          heart.remove();
        }, 8000);
      }, 3000);
    }

    // حساب "منذ لقائنا"
    const meetDate = new Date("2025-06-27T00:00:00");
    function calcSinceMeeting() {
      const now = new Date();
      let diff = now - meetDate;
      let days = Math.floor(diff / (1000 * 60 * 60 * 24));
      let months = Math.floor(days / 30.44);
      let years = Math.floor(months / 12);
      months = months % 12;
      let remainingDays = Math.floor(days - (years * 365.25) - (months * 30.44));

      let output = '';
      if (days === 0) {
        output = 'اليوم هو بداية قصتنا! 💖';
      } else if (days < 30) {
        output = `${days} يوم من الحب والسعادة ✨`;
      } else if (days < 365) {
        output = `${months} شهر و ${remainingDays} يوم من الذكريات الجميلة 💕`;
      } else {
        output = `${years} سنة و ${months} شهر و ${remainingDays} يوم من الحب الأبدي 💖`;
      }

      document.getElementById("sinceMeeting").textContent = output;
    }

    function getTimeUntil(month, day) {
      const now = new Date();
      let year = now.getFullYear();
      let target = new Date(year, month - 1, day);
      if (target < now) target.setFullYear(year + 1);

      let total = (target - now);
      let seconds = Math.floor(total / 1000);
      let minutes = Math.floor(seconds / 60);
      let hours = Math.floor(minutes / 60);
      let days = Math.floor(hours / 24);
      let months = Math.floor(days / 30.44);
      let remDays = days - months * 30.44;
      seconds = seconds % 60;
      minutes = minutes % 60;
      hours = hours % 24;

      let timeStr = '';
      if (total <= 0) {
        timeStr = 'عيد ميلاد سعيد! 🎉🎂';
      } else if (days === 0) {
        timeStr = `اليوم هو العيد! ${hours} ساعة و ${minutes} دقيقة متبقية 🎉`;
      } else if (days < 30) {
        timeStr = `${days} يوم، ${hours} ساعة، ${minutes} دقيقة، ${seconds} ثانية`;
      } else {
        timeStr = `${months} شهر و ${Math.floor(remDays)} يوم، ${hours} ساعة، ${minutes} دقيقة`;
      }
      return timeStr;
    }

    function updateCountdowns() {
      const now = new Date();
      calcSinceMeeting();
      document.getElementById("ayaCountdown").textContent = getTimeUntil(11, 8);
      document.getElementById("khalilCountdown").textContent = getTimeUntil(10, 28);
    }

    // تأثير تفاعلي للكروت
    function addCardInteractions() {
      const cards = document.querySelectorAll('.card');
      cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.2) + 's';

        card.addEventListener('mouseenter', () => {
          card.style.transform = 'translateY(-15px) scale(1.03)';
        });

        card.addEventListener('mouseleave', () => {
          card.style.transform = 'translateY(0) scale(1)';
        });
      });
    }

    // تشغيل كل شيء
    document.addEventListener('DOMContentLoaded', () => {
      createFloatingHearts();
      addCardInteractions();
      updateCountdowns();
      setInterval(updateCountdowns, 1000);
    });
  </script>
</body>
</html>