// إعدادات التطبيق
const CONFIG = {
    meetingDate: new Date('2025-06-27T00:00:00'),
    ayaBirthday: { month: 11, day: 8 },
    khalilBirthday: { month: 10, day: 28 },
    particleCount: window.innerWidth > 768 ? 50 : 20,
    updateInterval: 1000
};

// فئة إدارة العدادات
class CountdownManager {
    constructor() {
        this.elements = {
            meeting: document.getElementById('meetingCounter'),
            aya: document.getElementById('ayaBirthday'),
            khalil: document.getElementById('khalilBirthday')
        };
        this.init();
    }

    init() {
        this.updateCountdowns();
        setInterval(() => this.updateCountdowns(), CONFIG.updateInterval);
    }

    formatNumber(num) {
        return num.toLocaleString('ar-EG');
    }

    calculateMeetingTime() {
        const now = new Date();
        const diff = CONFIG.meetingDate - now;
        const totalDays = Math.floor(Math.abs(diff) / (1000 * 60 * 60 * 24));

        if (diff > 0) {
            // المستقبل - عد تنازلي
            return this.formatFutureTime(totalDays);
        } else if (diff < 0) {
            // الماضي - منذ اللقاء
            return this.formatPastTime(totalDays);
        } else {
            return 'اليوم هو لقاؤنا الأول! 💖';
        }
    }

    formatFutureTime(days) {
        if (days === 0) {
            return 'اليوم هو لقاؤنا الأول! 💖';
        } else if (days < 30) {
            return `${this.formatNumber(days)} يوم حتى لقائنا الأول ✨`;
        } else if (days < 365) {
            const months = Math.floor(days / 30.44);
            const remainingDays = Math.floor(days - (months * 30.44));
            
            if (remainingDays === 0) {
                return `${this.formatNumber(months)} شهر حتى لقائنا الأول 💫`;
            } else {
                return `${this.formatNumber(months)} شهر و ${this.formatNumber(remainingDays)} يوم حتى لقائنا الأول 💫`;
            }
        } else {
            const years = Math.floor(days / 365.25);
            const remainingDays = days - (years * 365.25);
            const months = Math.floor(remainingDays / 30.44);
            const finalDays = Math.floor(remainingDays - (months * 30.44));
            
            let result = `${this.formatNumber(years)} سنة`;
            if (months > 0) result += ` و ${this.formatNumber(months)} شهر`;
            if (finalDays > 0) result += ` و ${this.formatNumber(finalDays)} يوم`;
            return result + ' حتى لقائنا الأول 💖';
        }
    }

    formatPastTime(days) {
        if (days < 30) {
            return `${this.formatNumber(days)} يوم من الحب والسعادة ✨`;
        } else if (days < 365) {
            const months = Math.floor(days / 30.44);
            const remainingDays = Math.floor(days - (months * 30.44));
            
            if (remainingDays === 0) {
                return `${this.formatNumber(months)} شهر من الذكريات الجميلة 💕`;
            } else {
                return `${this.formatNumber(months)} شهر و ${this.formatNumber(remainingDays)} يوم من الذكريات الجميلة 💕`;
            }
        } else {
            const years = Math.floor(days / 365.25);
            const remainingDays = days - (years * 365.25);
            const months = Math.floor(remainingDays / 30.44);
            const finalDays = Math.floor(remainingDays - (months * 30.44));
            
            let result = `${this.formatNumber(years)} سنة`;
            if (months > 0) result += ` و ${this.formatNumber(months)} شهر`;
            if (finalDays > 0) result += ` و ${this.formatNumber(finalDays)} يوم`;
            return result + ' من الحب الأبدي 💖';
        }
    }

    calculateBirthday(month, day) {
        const now = new Date();
        let year = now.getFullYear();
        let target = new Date(year, month - 1, day);
        
        if (target < now) {
            target.setFullYear(year + 1);
        }

        const total = target - now;
        const totalDays = Math.floor(total / (1000 * 60 * 60 * 24));
        const hours = Math.floor((total % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((total % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((total % (1000 * 60)) / 1000);

        if (totalDays === 0) {
            if (hours === 0 && minutes === 0) {
                return 'عيد ميلاد سعيد! 🎉🎂';
            } else {
                return `اليوم هو العيد! ${this.formatNumber(hours)} ساعة و ${this.formatNumber(minutes)} دقيقة متبقية 🎉`;
            }
        } else if (totalDays < 30) {
            return `${this.formatNumber(totalDays)} يوم، ${this.formatNumber(hours)} ساعة، ${this.formatNumber(minutes)} دقيقة، ${this.formatNumber(seconds)} ثانية`;
        } else {
            const months = Math.floor(totalDays / 30.44);
            const remainingDays = Math.floor(totalDays - (months * 30.44));
            
            if (remainingDays === 0) {
                return `${this.formatNumber(months)} شهر، ${this.formatNumber(hours)} ساعة، ${this.formatNumber(minutes)} دقيقة`;
            } else {
                return `${this.formatNumber(months)} شهر و ${this.formatNumber(remainingDays)} يوم، ${this.formatNumber(hours)} ساعة، ${this.formatNumber(minutes)} دقيقة`;
            }
        }
    }

    updateCountdowns() {
        try {
            if (this.elements.meeting) {
                this.elements.meeting.innerHTML = this.calculateMeetingTime();
            }
            
            if (this.elements.aya) {
                this.elements.aya.innerHTML = this.calculateBirthday(CONFIG.ayaBirthday.month, CONFIG.ayaBirthday.day);
            }
            
            if (this.elements.khalil) {
                this.elements.khalil.innerHTML = this.calculateBirthday(CONFIG.khalilBirthday.month, CONFIG.khalilBirthday.day);
            }
        } catch (error) {
            console.error('خطأ في تحديث العدادات:', error);
        }
    }
}

// فئة إدارة الجزيئات
class ParticleSystem {
    constructor() {
        this.container = document.getElementById('particles');
        this.particles = [];
        this.init();
    }

    init() {
        if (!this.container) return;
        
        this.createParticles();
        this.startAnimation();
    }

    createParticles() {
        for (let i = 0; i < CONFIG.particleCount; i++) {
            setTimeout(() => {
                this.createParticle();
            }, i * 100);
        }
    }

    createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // موقع عشوائي
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDuration = (Math.random() * 5 + 8) + 's';
        particle.style.animationDelay = Math.random() * 5 + 's';
        
        // حجم متنوع
        const size = Math.random() * 3 + 2;
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        
        // شفافية متنوعة
        particle.style.opacity = Math.random() * 0.5 + 0.3;
        
        this.container.appendChild(particle);
        this.particles.push(particle);
        
        // إزالة الجزيء بعد انتهاء الأنيميشن
        setTimeout(() => {
            if (particle.parentNode) {
                particle.remove();
                this.particles = this.particles.filter(p => p !== particle);
            }
        }, (parseFloat(particle.style.animationDuration) + parseFloat(particle.style.animationDelay)) * 1000);
    }

    startAnimation() {
        setInterval(() => {
            if (this.particles.length < CONFIG.particleCount) {
                this.createParticle();
            }
        }, 2000);
    }

    destroy() {
        this.particles.forEach(particle => {
            if (particle.parentNode) {
                particle.remove();
            }
        });
        this.particles = [];
    }
}

// فئة إدارة التطبيق الرئيسية
class LoveApp {
    constructor() {
        this.countdownManager = null;
        this.particleSystem = null;
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.countdownManager = new CountdownManager();
            this.particleSystem = new ParticleSystem();
            this.setupEventListeners();
        });
    }

    setupEventListeners() {
        // تنظيف الموارد عند إغلاق الصفحة
        window.addEventListener('beforeunload', () => {
            if (this.particleSystem) {
                this.particleSystem.destroy();
            }
        });

        // تحسين الأداء عند تغيير حجم النافذة
        window.addEventListener('resize', () => {
            CONFIG.particleCount = window.innerWidth > 768 ? 50 : 20;
        });

        // إضافة تأثيرات تفاعلية للكروت
        document.querySelectorAll('.love-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
            });
        });
    }
}

// تشغيل التطبيق
const app = new LoveApp();
