// تطبيق فاخر عالمي المستوى
class LuxuryLoveApp {
    constructor() {
        this.config = {
            meetingDate: new Date('2025-06-27T00:00:00'),
            ayaBirthday: { month: 11, day: 8 },
            khalilBirthday: { month: 10, day: 28 },
            updateInterval: 1000,
            luxuryEffects: true
        };
        
        this.elements = {
            meeting: document.getElementById('meetingCounter'),
            aya: document.getElementById('ayaBirthday'),
            khalil: document.getElementById('khalilBirthday')
        };
        
        this.init();
    }

    init() {
        this.setupLuxuryEffects();
        this.updateCountdowns();
        setInterval(() => this.updateCountdowns(), this.config.updateInterval);
        this.addInteractiveEffects();
    }

    formatNumber(num) {
        return num.toLocaleString('ar-EG');
    }

    calculateMeetingTime() {
        const now = new Date();
        const diff = this.config.meetingDate - now;
        const totalDays = Math.floor(Math.abs(diff) / (1000 * 60 * 60 * 24));

        if (diff > 0) {
            return this.formatFutureTime(totalDays);
        } else if (diff < 0) {
            return this.formatPastTime(totalDays);
        } else {
            return '👑 اليوم هو لقاؤنا الملكي الأول! 💎';
        }
    }

    formatFutureTime(days) {
        if (days === 0) {
            return '👑 اليوم هو لقاؤنا الملكي الأول! 💎';
        } else if (days < 30) {
            return `✨ ${this.formatNumber(days)} يوم حتى اللقاء الملكي الأول 👑`;
        } else if (days < 365) {
            const months = Math.floor(days / 30.44);
            const remainingDays = Math.floor(days - (months * 30.44));
            
            if (remainingDays === 0) {
                return `💎 ${this.formatNumber(months)} شهر حتى اللقاء الملكي 👑`;
            } else {
                return `💎 ${this.formatNumber(months)} شهر و ${this.formatNumber(remainingDays)} يوم حتى اللقاء الملكي 👑`;
            }
        } else {
            const years = Math.floor(days / 365.25);
            const remainingDays = days - (years * 365.25);
            const months = Math.floor(remainingDays / 30.44);
            const finalDays = Math.floor(remainingDays - (months * 30.44));
            
            let result = `👑 ${this.formatNumber(years)} سنة`;
            if (months > 0) result += ` و ${this.formatNumber(months)} شهر`;
            if (finalDays > 0) result += ` و ${this.formatNumber(finalDays)} يوم`;
            return result + ' حتى اللقاء الملكي الأول 💎';
        }
    }

    formatPastTime(days) {
        if (days < 30) {
            return `✨ ${this.formatNumber(days)} يوم من الحب الملكي والسعادة الأسطورية 👑`;
        } else if (days < 365) {
            const months = Math.floor(days / 30.44);
            const remainingDays = Math.floor(days - (months * 30.44));
            
            if (remainingDays === 0) {
                return `💎 ${this.formatNumber(months)} شهر من الذكريات الملكية الجميلة 👑`;
            } else {
                return `💎 ${this.formatNumber(months)} شهر و ${this.formatNumber(remainingDays)} يوم من الذكريات الملكية 👑`;
            }
        } else {
            const years = Math.floor(days / 365.25);
            const remainingDays = days - (years * 365.25);
            const months = Math.floor(remainingDays / 30.44);
            const finalDays = Math.floor(remainingDays - (months * 30.44));
            
            let result = `👑 ${this.formatNumber(years)} سنة`;
            if (months > 0) result += ` و ${this.formatNumber(months)} شهر`;
            if (finalDays > 0) result += ` و ${this.formatNumber(finalDays)} يوم`;
            return result + ' من الحب الملكي الأبدي 💎';
        }
    }

    calculateBirthday(month, day) {
        const now = new Date();
        let year = now.getFullYear();
        let target = new Date(year, month - 1, day);
        
        if (target < now) {
            target.setFullYear(year + 1);
        }

        const total = target - now;
        const totalDays = Math.floor(total / (1000 * 60 * 60 * 24));
        const hours = Math.floor((total % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((total % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((total % (1000 * 60)) / 1000);

        if (totalDays === 0) {
            if (hours === 0 && minutes === 0) {
                return '👑 عيد ميلاد ملكي سعيد! 🎉💎🎂';
            } else {
                return `🎉 اليوم هو العيد الملكي! ${this.formatNumber(hours)} ساعة و ${this.formatNumber(minutes)} دقيقة متبقية 👑`;
            }
        } else if (totalDays < 30) {
            return `💎 ${this.formatNumber(totalDays)} يوم، ${this.formatNumber(hours)} ساعة، ${this.formatNumber(minutes)} دقيقة، ${this.formatNumber(seconds)} ثانية 👑`;
        } else {
            const months = Math.floor(totalDays / 30.44);
            const remainingDays = Math.floor(totalDays - (months * 30.44));
            
            if (remainingDays === 0) {
                return `👑 ${this.formatNumber(months)} شهر، ${this.formatNumber(hours)} ساعة، ${this.formatNumber(minutes)} دقيقة 💎`;
            } else {
                return `👑 ${this.formatNumber(months)} شهر و ${this.formatNumber(remainingDays)} يوم، ${this.formatNumber(hours)} ساعة، ${this.formatNumber(minutes)} دقيقة 💎`;
            }
        }
    }

    updateCountdowns() {
        try {
            if (this.elements.meeting) {
                this.elements.meeting.innerHTML = this.calculateMeetingTime();
            }
            
            if (this.elements.aya) {
                this.elements.aya.innerHTML = this.calculateBirthday(this.config.ayaBirthday.month, this.config.ayaBirthday.day);
            }
            
            if (this.elements.khalil) {
                this.elements.khalil.innerHTML = this.calculateBirthday(this.config.khalilBirthday.month, this.config.khalilBirthday.day);
            }
        } catch (error) {
            console.error('خطأ في تحديث العدادات الفاخرة:', error);
        }
    }

    setupLuxuryEffects() {
        // إضافة جزيئات الماس المتلألئة
        this.createDiamondParticles();
        
        // إضافة تأثيرات الذهب المتحركة
        this.createGoldEffects();
    }

    createDiamondParticles() {
        const particleCount = window.innerWidth > 768 ? 30 : 15;
        
        for (let i = 0; i < particleCount; i++) {
            setTimeout(() => {
                this.createDiamondParticle();
            }, i * 200);
        }
        
        // إضافة جزيئات جديدة بشكل دوري
        setInterval(() => {
            if (document.querySelectorAll('.diamond-particle').length < particleCount) {
                this.createDiamondParticle();
            }
        }, 3000);
    }

    createDiamondParticle() {
        const particle = document.createElement('div');
        particle.className = 'diamond-particle';
        particle.innerHTML = '💎';
        
        particle.style.position = 'fixed';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.fontSize = (Math.random() * 15 + 15) + 'px';
        particle.style.zIndex = '1';
        particle.style.pointerEvents = 'none';
        particle.style.animation = `diamondFloat ${Math.random() * 5 + 8}s linear infinite`;
        particle.style.opacity = Math.random() * 0.6 + 0.4;
        
        document.body.appendChild(particle);
        
        setTimeout(() => {
            if (particle.parentNode) {
                particle.remove();
            }
        }, 13000);
    }

    createGoldEffects() {
        // إضافة تأثيرات ذهبية للعناصر
        const style = document.createElement('style');
        style.textContent = `
            @keyframes diamondFloat {
                0% {
                    transform: translateY(100vh) rotate(0deg);
                    opacity: 0;
                }
                10% {
                    opacity: 1;
                }
                90% {
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100px) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    addInteractiveEffects() {
        // تأثيرات تفاعلية فاخرة للكروت
        document.querySelectorAll('.luxury-card').forEach((card, index) => {
            card.addEventListener('mouseenter', () => {
                this.createLuxurySparkles(card);
                card.style.transform = 'translateY(-20px) scale(1.03) rotateY(5deg)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
            });
            
            card.addEventListener('click', () => {
                this.createCrownEffect(card);
            });
        });

        // تأثيرات للعنوان الرئيسي
        const mainTitle = document.querySelector('.main-title');
        if (mainTitle) {
            mainTitle.addEventListener('mouseenter', () => {
                this.createRoyalAura(mainTitle);
            });
        }
    }

    createLuxurySparkles(element) {
        const rect = element.getBoundingClientRect();
        const sparkleCount = 8;
        
        for (let i = 0; i < sparkleCount; i++) {
            const sparkle = document.createElement('div');
            sparkle.innerHTML = '✨';
            sparkle.style.position = 'fixed';
            sparkle.style.left = (rect.left + Math.random() * rect.width) + 'px';
            sparkle.style.top = (rect.top + Math.random() * rect.height) + 'px';
            sparkle.style.fontSize = '20px';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.zIndex = '1000';
            sparkle.style.animation = 'sparkleUp 2s ease-out forwards';
            
            document.body.appendChild(sparkle);
            
            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.remove();
                }
            }, 2000);
        }
        
        // إضافة CSS للأنيميشن
        if (!document.querySelector('#sparkle-style')) {
            const style = document.createElement('style');
            style.id = 'sparkle-style';
            style.textContent = `
                @keyframes sparkleUp {
                    0% {
                        transform: translateY(0) scale(0);
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(-50px) scale(1.5);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    createCrownEffect(element) {
        const crown = document.createElement('div');
        crown.innerHTML = '👑';
        crown.style.position = 'absolute';
        crown.style.top = '-30px';
        crown.style.left = '50%';
        crown.style.transform = 'translateX(-50%)';
        crown.style.fontSize = '30px';
        crown.style.animation = 'crownBounce 1s ease-out forwards';
        
        element.style.position = 'relative';
        element.appendChild(crown);
        
        setTimeout(() => {
            if (crown.parentNode) {
                crown.remove();
            }
        }, 1000);
        
        // إضافة CSS للتاج
        if (!document.querySelector('#crown-style')) {
            const style = document.createElement('style');
            style.id = 'crown-style';
            style.textContent = `
                @keyframes crownBounce {
                    0% {
                        transform: translateX(-50%) translateY(0) scale(0);
                        opacity: 0;
                    }
                    50% {
                        transform: translateX(-50%) translateY(-20px) scale(1.2);
                        opacity: 1;
                    }
                    100% {
                        transform: translateX(-50%) translateY(-10px) scale(1);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    createRoyalAura(element) {
        element.style.filter = 'drop-shadow(0 0 30px rgba(212, 175, 55, 0.8))';
        
        setTimeout(() => {
            element.style.filter = '';
        }, 2000);
    }
}

// تشغيل التطبيق الفاخر
document.addEventListener('DOMContentLoaded', () => {
    new LuxuryLoveApp();
});
