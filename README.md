# 💖 الخليل وآيات - موقع رومانسي

موقع ويب رومانسي وتفاعلي يعرض قصة حب جميلة مع عدادات زمنية ذكية وتأثيرات بصرية ساحرة.

## ✨ المميزات

### 📅 عدادات ذكية
- **منذ اللقاء الأول**: عد تنازلي حتى 27 يونيو 2025
- **أعياد الميلاد**: عدادات تنازلية لعيدي ميلاد آيات والخليل
- **عرض تدريجي**: إخفاء الأشهر عند أقل من 30 يوم، إظهار السنوات بعد 12 شهر

### 🎨 تأثيرات بصرية
- **قلوب متطايرة**: إيموجي ملونة تطير عبر الشاشة
- **جزيئات ضوئية**: نقاط مضيئة متحركة
- **حلقات نبض**: دوائر متوسعة ومتقلصة
- **خلفية متدرجة**: ألوان متحركة وموجات ناعمة

### 📱 تصميم متجاوب
- **جميع الشاشات**: من الهواتف إلى الشاشات الكبيرة
- **أداء محسن**: تقليل التأثيرات على الأجهزة الضعيفة
- **خطوط احتياطية**: دعم للمتصفحات القديمة

## 🗂️ هيكل المشروع

```
love-website/
├── index_new.html      # الملف الرئيسي
├── styles.css          # ملف التنسيقات
├── script.js           # ملف JavaScript
└── README.md           # هذا الملف
```

## 🚀 كيفية الاستخدام

1. **تحميل الملفات**: تأكد من وجود جميع الملفات في نفس المجلد
2. **فتح الموقع**: افتح `index_new.html` في المتصفح
3. **التخصيص**: يمكنك تعديل التواريخ في `script.js`

## ⚙️ التخصيص

### تغيير التواريخ
في ملف `script.js`:
```javascript
// تاريخ اللقاء الأول
const meetDate = new Date("2025-06-27T00:00:00");

// أعياد الميلاد (في دالة getTimeUntil)
getTimeUntil(11, 8);  // آيات - 8 نوفمبر
getTimeUntil(10, 28); // الخليل - 28 أكتوبر
```

### تخصيص الألوان
في ملف `styles.css`:
```css
/* تدرج الخلفية */
background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);

/* ألوان النصوص */
background: linear-gradient(45deg, #e74c3c, #f39c12, #9b59b6, #3498db);
```

## 🔧 تحسينات الأداء

### للأجهزة الضعيفة
- تقليل عدد الجزيئات والقلوب
- تسريع الأنيميشن أو إيقافها
- تحسين استهلاك الذاكرة

### للمتصفحات القديمة
- خطوط احتياطية محلية
- تدرج تحميل الخطوط
- معالجة الأخطاء الشاملة

## 📊 الأداء

- **حجم الملفات**: أقل من 50KB إجمالي
- **وقت التحميل**: أقل من 2 ثانية
- **استهلاك الذاكرة**: محسن للهواتف
- **توافق المتصفحات**: 95%+

## 🛠️ التقنيات المستخدمة

- **HTML5**: هيكل دلالي ومحسن لمحركات البحث
- **CSS3**: تأثيرات متقدمة وتصميم متجاوب
- **JavaScript ES6**: كود حديث ومحسن
- **Web Fonts**: خطوط عربية جميلة

## 📱 التوافق

### المتصفحات المدعومة
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Opera 47+

### أحجام الشاشات
- ✅ الهواتف (320px+)
- ✅ الأجهزة اللوحية (768px+)
- ✅ أجهزة الكمبيوتر (1024px+)
- ✅ الشاشات الكبيرة (1400px+)

## 🎯 المميزات المتقدمة

### إمكانية الوصول
- **ARIA labels**: للقارئات الصوتية
- **Semantic HTML**: هيكل منطقي
- **Keyboard navigation**: تنقل بلوحة المفاتيح
- **Reduced motion**: دعم تقليل الحركة

### SEO
- **Meta tags**: وصف وكلمات مفتاحية
- **Open Graph**: مشاركة على وسائل التواصل
- **Structured data**: بيانات منظمة
- **Fast loading**: تحميل سريع

## 📝 الترخيص

هذا المشروع مخصص للاستخدام الشخصي. جميع الحقوق محفوظة للخليل وآيات 💖

## 💡 أفكار للتطوير

- إضافة ألبوم صور
- تشغيل موسيقى خلفية
- رسائل حب يومية
- تقويم المناسبات
- مشاركة على وسائل التواصل

---

**صُنع بـ 💖 للخليل وآيات**
