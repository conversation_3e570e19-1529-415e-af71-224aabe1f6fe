<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>الخليل وآيات 💖</title>
  <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Cairo', 'Arial', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
      color: #333;
      text-align: center;
      padding: 2rem 1rem;
      min-height: 100vh;
      position: relative;
      overflow-x: hidden;
    }

    /* موجات الخلفية المحسنة */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 80%, rgba(255,107,107,0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(254,202,87,0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(72,219,251,0.15) 0%, transparent 50%),
        radial-gradient(circle at 60% 60%, rgba(255,159,243,0.15) 0%, transparent 50%);
      animation: backgroundWaves 20s ease infinite;
      pointer-events: none;
      z-index: -2;
    }

    body::after {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 70% 30%, rgba(255,107,107,0.1) 0%, transparent 60%),
        radial-gradient(circle at 30% 70%, rgba(254,202,87,0.1) 0%, transparent 60%),
        radial-gradient(circle at 90% 90%, rgba(72,219,251,0.1) 0%, transparent 60%),
        radial-gradient(circle at 10% 10%, rgba(255,159,243,0.1) 0%, transparent 60%);
      animation: backgroundWaves 25s ease infinite reverse;
      pointer-events: none;
      z-index: -2;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    @keyframes backgroundWaves {
      0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.4;
      }
      25% {
        transform: scale(1.1) rotate(90deg);
        opacity: 0.6;
      }
      50% {
        transform: scale(0.9) rotate(180deg);
        opacity: 0.5;
      }
      75% {
        transform: scale(1.05) rotate(270deg);
        opacity: 0.7;
      }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    h1 {
      font-family: 'Amiri', serif;
      font-size: 3.5rem;
      font-weight: 700;
      color: #fff;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      margin-bottom: 2rem;
      background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradientShift 8s ease infinite;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      padding: 0 1rem;
    }

    .card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 25px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      padding: 2.5rem 2rem;
      margin: 1rem 0;
      position: relative;
      overflow: visible;
      transition: all 0.4s ease;
      animation: fadeInUp 0.8s ease forwards;
      border: 1px solid rgba(255,255,255,0.3);
      z-index: 2;
    }

    /* موجات ملونة بسيطة حول الكروت */
    .card::before {
      content: '';
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      background: linear-gradient(45deg, 
        rgba(255,107,107,0.6), rgba(254,202,87,0.6), 
        rgba(72,219,251,0.6), rgba(255,159,243,0.6));
      background-size: 300% 300%;
      border-radius: 28px;
      z-index: -1;
      opacity: 0;
      transition: opacity 0.3s ease;
      animation: waveFlow 6s ease infinite;
    }

    .card.music-active::before {
      opacity: 1;
    }

    @keyframes waveFlow {
      0% { 
        background-position: 0% 50%;
        filter: blur(2px);
      }
      50% { 
        background-position: 100% 50%;
        filter: blur(4px);
      }
      100% { 
        background-position: 0% 50%;
        filter: blur(2px);
      }
    }

    .card:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 30px 60px rgba(0,0,0,0.15);
    }

    .card:hover::before {
      opacity: 1;
    }

    .card h2 {
      font-family: 'Amiri', serif;
      font-size: 1.8rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 1.5rem;
      position: relative;
    }

    .card h2::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      border-radius: 2px;
    }

    .big {
      font-size: 1.8rem;
      font-weight: 600;
      background: linear-gradient(45deg, #e74c3c, #f39c12, #9b59b6, #3498db);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradientShift 6s ease infinite;
      line-height: 1.4;
    }

    .note {
      color: #7f8c8d;
      font-style: italic;
      margin-top: 15px;
      font-size: 1.1rem;
    }

    .special-card {
      background: linear-gradient(135deg, rgba(255,107,107,0.1), rgba(254,202,87,0.1));
      border: 2px solid rgba(255,107,107,0.3);
    }

    .promise-card {
      background: linear-gradient(135deg, rgba(155,89,182,0.1), rgba(52,152,219,0.1));
      border: 2px solid rgba(155,89,182,0.3);
    }

    /* أزرار التحكم المبسطة */
    .music-controls {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 1000;
      background: rgba(255,255,255,0.9);
      backdrop-filter: blur(10px);
      border-radius: 50px;
      padding: 10px;
      border: 2px solid rgba(255,107,107,0.3);
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .music-btn {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: none;
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      color: white;
      font-size: 18px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .music-btn:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    }

    .music-btn.playing {
      animation: musicPulse 2s ease-in-out infinite;
    }

    @keyframes musicPulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    /* تصميم متجاوب */
    @media (max-width: 768px) {
      h1 {
        font-size: 2.5rem;
      }
      
      .container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
      
      .card {
        padding: 2rem 1.5rem;
      }
      
      .big {
        font-size: 1.5rem;
      }
      
      .music-controls {
        bottom: 15px;
        right: 15px;
      }
      
      .music-btn {
        width: 45px;
        height: 45px;
        font-size: 16px;
      }
    }

    @media (max-width: 480px) {
      h1 {
        font-size: 2rem;
      }
      
      .card {
        padding: 1.5rem 1rem;
      }
      
      .big {
        font-size: 1.3rem;
      }
    }
  </style>
</head>
<body>
  <!-- ملف الصوت -->
  <audio id="backgroundMusic" loop preload="auto">
    <source src="music/001.mp3" type="audio/mpeg">
  </audio>

  <!-- عناصر التحكم بالموسيقى -->
  <div class="music-controls">
    <button class="music-btn playing" id="playPauseBtn" title="تشغيل/إيقاف الموسيقى">
      ⏸️
    </button>
  </div>

  <h1>💖 الخليل وآيات 💖</h1>

  <div class="container">
    <div class="card music-reactive" style="animation-delay: 0.2s;">
      <h2>✨ منذ لقائنا الأول</h2>
      <div class="note">27 يونيو 2025</div>
      <div id="sinceMeeting" class="big"></div>
    </div>

    <div class="card music-reactive" style="animation-delay: 0.4s;">
      <h2>🎂 عيد ميلاد آيات الحبيبة</h2>
      <div class="note">8 نوفمبر 1999</div>
      <div id="ayaCountdown" class="big"></div>
    </div>

    <div class="card music-reactive" style="animation-delay: 0.6s;">
      <h2>🎉 عيد ميلاد الخليل العزيز</h2>
      <div class="note">28 أكتوبر 1999</div>
      <div id="khalilCountdown" class="big"></div>
    </div>

    <div class="card special-card music-reactive" style="animation-delay: 0.8s;">
      <h2>💫 حقيقة جميلة</h2>
      <div class="big">الخليل أكبر من آيات بـ 11 يوم فقط 👑💕</div>
      <div class="note">فارق عمر رومانسي وجميل</div>
    </div>

    <div class="card promise-card music-reactive" style="animation-delay: 1s;">
      <h2>💍 عهد الحب الأبدي</h2>
      <div class="big">معاً نحو المستقبل... الزواج والسعادة والحب الصادق 💖</div>
      <div class="note">وعد بالحب والاحترام والإخلاص</div>
    </div>

    <div class="card music-reactive" style="animation-delay: 1.2s;">
      <h2>🌟 رسالة حب</h2>
      <div class="big">كل لحظة معك هي ذكرى جميلة... وكل يوم قادم هو حلم نحققه سوياً 💕</div>
    </div>
  </div>
  <script>
    // متغيرات عامة
    let audioElement = document.getElementById('backgroundMusic');
    let playPauseBtn = document.getElementById('playPauseBtn');
    let isPlaying = true;
    let audioContext = null;
    let analyser = null;
    let dataArray = null;

    // حساب "منذ لقائنا"
    const meetDate = new Date("2025-06-27T00:00:00");
    function calcSinceMeeting() {
      const now = new Date();
      let diff = now - meetDate;
      let days = Math.floor(diff / (1000 * 60 * 60 * 24));
      let months = Math.floor(days / 30.44);
      let years = Math.floor(months / 12);
      months = months % 12;
      let remainingDays = Math.floor(days - (years * 365.25) - (months * 30.44));

      let output = '';
      if (days === 0) {
        output = 'اليوم هو بداية قصتنا! 💖';
      } else if (days < 30) {
        output = `${days} يوم من الحب والسعادة ✨`;
      } else if (days < 365) {
        output = `${months} شهر و ${remainingDays} يوم من الذكريات الجميلة 💕`;
      } else {
        output = `${years} سنة و ${months} شهر و ${remainingDays} يوم من الحب الأبدي 💖`;
      }

      document.getElementById("sinceMeeting").textContent = output;
    }

    function getTimeUntil(month, day) {
      const now = new Date();
      let year = now.getFullYear();
      let target = new Date(year, month - 1, day);
      if (target < now) target.setFullYear(year + 1);

      let total = (target - now);
      let seconds = Math.floor(total / 1000);
      let minutes = Math.floor(seconds / 60);
      let hours = Math.floor(minutes / 60);
      let days = Math.floor(hours / 24);
      let months = Math.floor(days / 30.44);
      let remDays = days - months * 30.44;
      seconds = seconds % 60;
      minutes = minutes % 60;
      hours = hours % 24;

      let timeStr = '';
      if (total <= 0) {
        timeStr = 'عيد ميلاد سعيد! 🎉🎂';
      } else if (days === 0) {
        timeStr = `اليوم هو العيد! ${hours} ساعة و ${minutes} دقيقة متبقية 🎉`;
      } else if (days < 30) {
        timeStr = `${days} يوم، ${hours} ساعة، ${minutes} دقيقة، ${seconds} ثانية`;
      } else {
        timeStr = `${months} شهر و ${Math.floor(remDays)} يوم، ${hours} ساعة، ${minutes} دقيقة`;
      }
      return timeStr;
    }

    function updateCountdowns() {
      calcSinceMeeting();
      document.getElementById("ayaCountdown").textContent = getTimeUntil(11, 8);
      document.getElementById("khalilCountdown").textContent = getTimeUntil(10, 28);
    }

    // إعداد الموسيقى
    function setupAudio() {
      try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyser = audioContext.createAnalyser();
        analyser.fftSize = 256;
        dataArray = new Uint8Array(analyser.frequencyBinCount);

        const source = audioContext.createMediaElementSource(audioElement);
        source.connect(analyser);
        analyser.connect(audioContext.destination);
      } catch (error) {
        console.log('Web Audio API غير مدعوم');
      }
    }

    // تشغيل/إيقاف الموسيقى
    function toggleMusic() {
      if (isPlaying) {
        audioElement.pause();
        playPauseBtn.textContent = '▶️';
        playPauseBtn.classList.remove('playing');
        isPlaying = false;

        // إزالة التأثيرات من الكروت
        document.querySelectorAll('.card').forEach(card => {
          card.classList.remove('music-active');
        });
      } else {
        if (audioContext && audioContext.state === 'suspended') {
          audioContext.resume();
        }

        audioElement.play().then(() => {
          playPauseBtn.textContent = '⏸️';
          playPauseBtn.classList.add('playing');
          isPlaying = true;
          startMusicEffects();
        }).catch(error => {
          console.log('فشل في تشغيل الموسيقى:', error);
        });
      }
    }

    // تأثيرات الموسيقى البسيطة
    function startMusicEffects() {
      if (!isPlaying) return;

      const cards = document.querySelectorAll('.card.music-reactive');

      // تفعيل التأثيرات على الكروت
      cards.forEach((card, index) => {
        setTimeout(() => {
          if (isPlaying) {
            card.classList.add('music-active');
          }
        }, index * 200);
      });

      // تأثير نبض بسيط
      setInterval(() => {
        if (isPlaying) {
          const intensity = Math.random() * 0.5 + 0.5;

          cards.forEach((card, index) => {
            const delay = index * 100;
            setTimeout(() => {
              if (isPlaying) {
                const scale = 1 + intensity * 0.02;
                card.style.transform = `scale(${scale})`;

                setTimeout(() => {
                  if (isPlaying) {
                    card.style.transform = 'scale(1)';
                  }
                }, 300);
              }
            }, delay);
          });
        }
      }, 2000);
    }

    // إعداد الأحداث
    playPauseBtn.addEventListener('click', toggleMusic);

    // تشغيل تلقائي عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', () => {
      updateCountdowns();
      setInterval(updateCountdowns, 1000);

      // تشغيل الموسيقى تلقائياً بعد ثانية
      setTimeout(() => {
        setupAudio();
        audioElement.play().then(() => {
          startMusicEffects();
        }).catch(() => {
          // في حالة فشل التشغيل التلقائي
          playPauseBtn.textContent = '▶️';
          playPauseBtn.classList.remove('playing');
          isPlaying = false;
        });
      }, 1000);
    });

    // تحسين الأداء - تقليل استخدام الذاكرة
    window.addEventListener('beforeunload', () => {
      if (audioContext) {
        audioContext.close();
      }
    });
  </script>
</body>
</html>
