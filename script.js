// تاريخ اللقاء الأول
const meetDate = new Date("2025-06-27T00:00:00");

// دالة مساعدة لتحسين عرض الأرقام
function formatNumber(num) {
  return num.toLocaleString('ar-EG');
}

// حساب "منذ لقائنا الأول"
function calcSinceMeeting() {
  try {
    const now = new Date();
    let diff = now - meetDate;
    let totalDays = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    let output = '';
    
    if (diff < 0) {
      // التاريخ في المستقبل
      totalDays = Math.abs(totalDays);
      if (totalDays === 0) {
        output = 'اليوم هو لقاؤنا الأول! 💖';
      } else if (totalDays < 30) {
        output = `${formatNumber(totalDays)} يوم حتى لقائنا الأول 💖`;
      } else if (totalDays < 365) {
        let months = Math.floor(totalDays / 30.44);
        let remainingDays = Math.floor(totalDays - (months * 30.44));
        if (remainingDays === 0) {
          output = `${formatNumber(months)} شهر حتى لقائنا الأول 💖`;
        } else {
          output = `${formatNumber(months)} شهر و ${formatNumber(remainingDays)} يوم حتى لقائنا الأول 💖`;
        }
      } else {
        let years = Math.floor(totalDays / 365.25);
        let remainingDaysAfterYears = totalDays - (years * 365.25);
        let months = Math.floor(remainingDaysAfterYears / 30.44);
        let days = Math.floor(remainingDaysAfterYears - (months * 30.44));
        
        if (months === 0 && days === 0) {
          output = `${formatNumber(years)} سنة حتى لقائنا الأول 💖`;
        } else if (days === 0) {
          output = `${formatNumber(years)} سنة و ${formatNumber(months)} شهر حتى لقائنا الأول 💖`;
        } else if (months === 0) {
          output = `${formatNumber(years)} سنة و ${formatNumber(days)} يوم حتى لقائنا الأول 💖`;
        } else {
          output = `${formatNumber(years)} سنة و ${formatNumber(months)} شهر و ${formatNumber(days)} يوم حتى لقائنا الأول 💖`;
        }
      }
    } else if (totalDays === 0) {
      output = 'اليوم هو بداية قصتنا! 💖';
    } else if (totalDays < 30) {
      // أقل من 30 يوم - عرض الأيام فقط
      output = `${formatNumber(totalDays)} يوم من الحب والسعادة ✨`;
    } else if (totalDays < 365) {
      // من 30 يوم إلى سنة - عرض الأشهر والأيام
      let months = Math.floor(totalDays / 30.44);
      let remainingDays = Math.floor(totalDays - (months * 30.44));
      
      if (remainingDays === 0) {
        output = `${formatNumber(months)} شهر من الذكريات الجميلة 💕`;
      } else {
        output = `${formatNumber(months)} شهر و ${formatNumber(remainingDays)} يوم من الذكريات الجميلة 💕`;
      }
    } else {
      // أكثر من سنة - عرض السنوات والأشهر والأيام
      let years = Math.floor(totalDays / 365.25);
      let remainingDaysAfterYears = totalDays - (years * 365.25);
      let months = Math.floor(remainingDaysAfterYears / 30.44);
      let days = Math.floor(remainingDaysAfterYears - (months * 30.44));
      
      if (months === 0 && days === 0) {
        output = `${formatNumber(years)} سنة من الحب الأبدي 💖`;
      } else if (days === 0) {
        output = `${formatNumber(years)} سنة و ${formatNumber(months)} شهر من الحب الأبدي 💖`;
      } else if (months === 0) {
        output = `${formatNumber(years)} سنة و ${formatNumber(days)} يوم من الحب الأبدي 💖`;
      } else {
        output = `${formatNumber(years)} سنة و ${formatNumber(months)} شهر و ${formatNumber(days)} يوم من الحب الأبدي 💖`;
      }
    }
    
    const element = document.getElementById("sinceMeeting");
    if (element) {
      element.textContent = output;
    }
  } catch (error) {
    console.log('خطأ في حساب التاريخ:', error);
    const element = document.getElementById("sinceMeeting");
    if (element) {
      element.textContent = 'قصة حب جميلة 💖';
    }
  }
}

// حساب العد التنازلي لأعياد الميلاد
function getTimeUntil(month, day) {
  try {
    const now = new Date();
    let year = now.getFullYear();
    let target = new Date(year, month - 1, day);
    
    // إذا كان التاريخ قد مر هذا العام، انتقل للعام القادم
    if (target < now) target.setFullYear(year + 1);

    let total = (target - now);
    
    // التحقق من صحة التاريخ
    if (isNaN(total) || total < 0) {
      return 'تاريخ غير صحيح 📅';
    }
    
    let seconds = Math.floor(total / 1000);
    let minutes = Math.floor(seconds / 60);
    let hours = Math.floor(minutes / 60);
    let totalDays = Math.floor(hours / 24);
    
    seconds = seconds % 60;
    minutes = minutes % 60;
    hours = hours % 24;

    let timeStr = '';
    
    if (total <= 86400000) { // أقل من يوم واحد
      if (hours === 0 && minutes === 0) {
        timeStr = `عيد ميلاد سعيد! 🎉🎂`;
      } else if (hours === 0) {
        timeStr = `اليوم هو العيد! ${formatNumber(minutes)} دقيقة متبقية 🎉`;
      } else {
        timeStr = `اليوم هو العيد! ${formatNumber(hours)} ساعة و ${formatNumber(minutes)} دقيقة متبقية 🎉`;
      }
    } else if (totalDays < 30) {
      // أقل من 30 يوم - عرض الأيام والساعات والدقائق والثواني فقط (بدون أشهر)
      timeStr = `${formatNumber(totalDays)} يوم، ${formatNumber(hours)} ساعة، ${formatNumber(minutes)} دقيقة، ${formatNumber(seconds)} ثانية`;
    } else {
      // 30 يوم أو أكثر - عرض الأشهر والأيام والساعات والدقائق (إخفاء الثواني)
      let months = Math.floor(totalDays / 30.44);
      let remainingDays = Math.floor(totalDays - (months * 30.44));
      
      if (remainingDays === 0) {
        timeStr = `${formatNumber(months)} شهر، ${formatNumber(hours)} ساعة، ${formatNumber(minutes)} دقيقة`;
      } else {
        timeStr = `${formatNumber(months)} شهر و ${formatNumber(remainingDays)} يوم، ${formatNumber(hours)} ساعة، ${formatNumber(minutes)} دقيقة`;
      }
    }
    
    return timeStr;
  } catch (error) {
    console.log('خطأ في حساب العد التنازلي:', error);
    return 'قريباً... 🎂';
  }
}

// تحديث جميع العدادات
function updateCountdowns() {
  try {
    calcSinceMeeting();
    
    const ayaElement = document.getElementById("ayaCountdown");
    const khalilElement = document.getElementById("khalilCountdown");
    
    if (ayaElement) {
      ayaElement.textContent = getTimeUntil(11, 8);
    }
    
    if (khalilElement) {
      khalilElement.textContent = getTimeUntil(10, 28);
    }
  } catch (error) {
    console.log('خطأ في تحديث العدادات:', error);
  }
}

// إنشاء القلوب المتطايرة - محسن للأداء
function createFloatingHearts() {
  const heartsContainer = document.getElementById('floatingHearts');
  if (!heartsContainer) return;
  
  // تقليل عدد الإيموجي لتحسين الأداء
  const hearts = ['💖', '💕', '💗', '💓', '💝', '🌹', '✨', '💫'];
  let heartCount = 0;
  const maxHearts = 15; // حد أقصى للقلوب في الشاشة
  
  // تحديد العدد والحجم حسب حجم الشاشة
  const screenWidth = window.innerWidth;
  let heartsPerInterval, minSize, maxSize, interval;
  
  if (screenWidth <= 480) {
    heartsPerInterval = 1;
    minSize = 18;
    maxSize = 25;
    interval = 3000; // تقليل التردد
  } else if (screenWidth <= 768) {
    heartsPerInterval = 1;
    minSize = 22;
    maxSize = 30;
    interval = 2500;
  } else {
    heartsPerInterval = 2;
    minSize = 25;
    maxSize = 35; // تقليل الحد الأقصى
    interval = 2000;
  }
  
  const heartInterval = setInterval(() => {
    // تنظيف القلوب الزائدة
    if (heartCount >= maxHearts) {
      const oldHearts = heartsContainer.querySelectorAll('.heart');
      if (oldHearts.length > 0) {
        oldHearts[0].remove();
        heartCount--;
      }
    }
    
    for (let i = 0; i < heartsPerInterval && heartCount < maxHearts; i++) {
      try {
        const heart = document.createElement('div');
        heart.className = 'heart';
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.left = Math.random() * 100 + '%';
        heart.style.animationDuration = (Math.random() * 3 + 5) + 's';
        
        const fontSize = Math.random() * (maxSize - minSize) + minSize;
        heart.style.fontSize = fontSize + 'px';
        
        const hue = Math.random() * 360;
        heart.style.color = `hsl(${hue}, 70%, 70%)`;
        heart.style.textShadow = `0 0 8px currentColor`;
        
        heartsContainer.appendChild(heart);
        heartCount++;
        
        // إزالة القلب بعد انتهاء الأنيميشن
        setTimeout(() => {
          if (heart.parentNode) {
            heart.remove();
            heartCount--;
          }
        }, parseInt(heart.style.animationDuration) * 1000);
      } catch (error) {
        console.log('خطأ في إنشاء القلب:', error);
      }
    }
  }, interval);
  
  // تنظيف عند إغلاق الصفحة
  window.addEventListener('beforeunload', () => {
    clearInterval(heartInterval);
  });
}

// إنشاء الجزيئات المتطايرة - محسن للأداء
function createParticles() {
  const particlesContainer = document.getElementById('musicParticles');
  if (!particlesContainer) return;
  
  // تقليل العدد لتحسين الأداء
  const particleCount = window.innerWidth <= 768 ? 15 : 25;
  const maxParticles = window.innerWidth <= 768 ? 20 : 30;
  
  for (let i = 0; i < particleCount; i++) {
    try {
      const particle = document.createElement('div');
      particle.className = 'particle';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
      particle.style.animationDelay = (Math.random() * 5) + 's';
      particle.style.opacity = Math.random() * 0.4 + 0.4;
      
      particlesContainer.appendChild(particle);
    } catch (error) {
      console.log('خطأ في إنشاء الجزيء:', error);
    }
  }
  
  // إضافة جزيئات بشكل محدود
  const particleInterval = setInterval(() => {
    if (particlesContainer.children.length < maxParticles) {
      try {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
        particle.style.animationDelay = '0s';
        particle.style.opacity = Math.random() * 0.4 + 0.4;
        
        particlesContainer.appendChild(particle);
        
        // إزالة الجزيء بعد انتهاء الأنيميشن
        setTimeout(() => {
          if (particle.parentNode) {
            particle.remove();
          }
        }, parseInt(particle.style.animationDuration) * 1000);
      } catch (error) {
        console.log('خطأ في إنشاء جزيء إضافي:', error);
      }
    }
  }, 5000); // تقليل التردد إلى كل 5 ثوان
  
  // تنظيف عند إغلاق الصفحة
  window.addEventListener('beforeunload', () => {
    clearInterval(particleInterval);
  });
}

// تحسين الأداء وإدارة الذاكرة
function cleanupResources() {
  try {
    // إيقاف جميع الفواصل الزمنية
    const intervals = window.setInterval(() => {}, 1000);
    for (let i = 1; i <= intervals; i++) {
      window.clearInterval(i);
    }
    
    // تنظيف العناصر المتحركة
    const heartsContainer = document.getElementById('floatingHearts');
    const particlesContainer = document.getElementById('musicParticles');
    
    if (heartsContainer) {
      heartsContainer.innerHTML = '';
    }
    
    if (particlesContainer) {
      particlesContainer.innerHTML = '';
    }
    
    console.log('تم تنظيف الموارد بنجاح');
  } catch (error) {
    console.log('خطأ في تنظيف الموارد:', error);
  }
}

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', cleanupResources);
window.addEventListener('pagehide', cleanupResources);

// تشغيل كل شيء عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  // تشغيل التأثيرات البصرية
  createFloatingHearts();
  createParticles();
  
  // تشغيل العدادات
  updateCountdowns();
  setInterval(updateCountdowns, 1000);
});
