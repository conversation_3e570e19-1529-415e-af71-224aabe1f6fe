/* خطوط احتياطية في حالة فشل تحميل الخطوط الخارجية */
@font-face {
  font-family: 'Cairo-Fallback';
  src: local('Tahoma'), local('Arial Unicode MS'), local('Arial');
  font-display: swap;
}

@font-face {
  font-family: 'Amiri-Fallback';
  src: local('Times New Roman'), local('Georgia'), local('serif');
  font-display: swap;
}

/* إعدادات عامة */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Cairo-Fallback', 'Tahoma', 'Arial', sans-serif;
  background:
    linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%),
    linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(ellipse at center, rgba(255,255,255,0.1) 0%, transparent 70%);
  background-size: 400% 400%, 100% 100%, 100% 100%;
  animation: gradientShift 15s ease infinite;
  color: #333;
  text-align: center;
  padding: 3rem 2rem;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* الخلفية الجميلة المدمجة مع القلوب والموجات */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.1)">💖</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>'),
    radial-gradient(circle at 20% 80%, rgba(255,107,107,0.2) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(254,202,87,0.2) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(72,219,251,0.2) 0%, transparent 50%),
    radial-gradient(circle at 60% 60%, rgba(255,159,243,0.2) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(255,107,107,0.15) 0%, transparent 60%),
    radial-gradient(circle at 30% 70%, rgba(254,202,87,0.15) 0%, transparent 60%),
    radial-gradient(circle at 90% 90%, rgba(72,219,251,0.15) 0%, transparent 60%),
    radial-gradient(circle at 10% 10%, rgba(255,159,243,0.15) 0%, transparent 60%);
  animation: backgroundWaves 20s ease infinite;
  pointer-events: none;
  z-index: -2;
}

body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 15% 15%, rgba(255,107,107,0.12) 0%, transparent 45%),
    radial-gradient(circle at 85% 85%, rgba(254,202,87,0.12) 0%, transparent 45%),
    radial-gradient(circle at 50% 20%, rgba(72,219,251,0.12) 0%, transparent 45%),
    radial-gradient(circle at 50% 80%, rgba(255,159,243,0.12) 0%, transparent 45%),
    linear-gradient(45deg, rgba(255,255,255,0.05) 0%, transparent 30%, rgba(255,255,255,0.05) 70%);
  animation: backgroundWaves 25s ease infinite reverse;
  pointer-events: none;
  z-index: -3;
}

/* الأنيميشن المحسن */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%, 0% 0%, 0% 0%;
    filter: hue-rotate(0deg);
  }
  25% {
    background-position: 25% 75%, 100% 0%, 50% 50%;
    filter: hue-rotate(90deg);
  }
  50% {
    background-position: 100% 50%, 0% 100%, 100% 100%;
    filter: hue-rotate(180deg);
  }
  75% {
    background-position: 75% 25%, 100% 100%, 50% 50%;
    filter: hue-rotate(270deg);
  }
  100% {
    background-position: 0% 50%, 0% 0%, 0% 0%;
    filter: hue-rotate(360deg);
  }
}

@keyframes backgroundWaves {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: scale(1.05) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(0.95) rotate(180deg);
    opacity: 0.7;
  }
  75% {
    transform: scale(1.02) rotate(270deg);
    opacity: 0.9;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* العنوان الرئيسي */
h1 {
  font-family: 'Amiri', 'Amiri-Fallback', 'Times New Roman', serif;
  font-size: 3.5rem;
  font-weight: 700;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 8s ease infinite;
}

/* الحاوية الرئيسية */
.container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  padding: 2rem;
  width: 100%;
  box-sizing: border-box;
}

/* الكروت */
.card {
  background:
    rgba(255, 255, 255, 0.95),
    linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  box-shadow:
    0 20px 40px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.6),
    0 0 0 1px rgba(255,255,255,0.2);
  padding: 2.5rem 2rem;
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  animation: fadeInUp 0.8s ease forwards;
  z-index: 2;
  width: 100%;
  box-sizing: border-box;
}

.card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow:
    0 30px 60px rgba(0,0,0,0.15),
    inset 0 1px 0 rgba(255,255,255,0.8),
    0 0 0 1px rgba(255,255,255,0.4),
    0 0 30px rgba(255,107,107,0.2);
  background:
    rgba(255, 255, 255, 0.98),
    linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%);
}

.card h2 {
  font-family: 'Amiri', 'Amiri-Fallback', 'Times New Roman', serif;
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  position: relative;
}

.card h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  border-radius: 2px;
}

.big {
  font-size: 1.8rem;
  font-weight: 600;
  background: linear-gradient(45deg, #e74c3c, #f39c12, #9b59b6, #3498db);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 6s ease infinite;
  line-height: 1.4;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.note {
  color: #7f8c8d;
  font-style: italic;
  margin-top: 15px;
  font-size: 1.1rem;
}

.special-card {
  background: linear-gradient(135deg, rgba(255,107,107,0.1), rgba(254,202,87,0.1));
  border: 2px solid rgba(255,107,107,0.3);
}

.promise-card {
  background: linear-gradient(135deg, rgba(155,89,182,0.1), rgba(52,152,219,0.1));
  border: 2px solid rgba(155,89,182,0.3);
}

/* القلوب والجزيئات المتطايرة */
.floating-hearts {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.heart {
  position: absolute;
  font-size: 28px;
  color: rgba(255,255,255,0.6);
  animation: floatUp 8s linear infinite;
  transition: all 0.3s ease;
  text-shadow: 0 0 10px currentColor;
}

@keyframes floatUp {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.music-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(255,107,107,0.9), transparent);
  border-radius: 50%;
  animation: particle-float 8s linear infinite;
  box-shadow: 0 0 8px currentColor;
}

.particle:nth-child(2n) {
  background: radial-gradient(circle, rgba(254,202,87,0.9), transparent);
  animation-duration: 10s;
  width: 5px;
  height: 5px;
}

.particle:nth-child(3n) {
  background: radial-gradient(circle, rgba(72,219,251,0.9), transparent);
  animation-duration: 12s;
  width: 7px;
  height: 7px;
}

.particle:nth-child(4n) {
  background: radial-gradient(circle, rgba(255,159,243,0.9), transparent);
  animation-duration: 6s;
  width: 5px;
  height: 5px;
}

.particle:nth-child(5n) {
  background: radial-gradient(circle, rgba(155,89,182,0.9), transparent);
  animation-duration: 9s;
  width: 6px;
  height: 6px;
}

@keyframes particle-float {
  0% {
    transform: translateY(100vh) translateX(0) scale(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: scale(1);
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(50px) scale(0) rotate(360deg);
    opacity: 0;
  }
}

/* حلقات النبض الجميلة */
.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid rgba(255,255,255,0.2);
  border-radius: 50%;
  animation: pulse-ring 4s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

.pulse-ring:nth-child(2) {
  width: 300px;
  height: 300px;
  animation-delay: 1s;
  opacity: 0.5;
}

.pulse-ring:nth-child(3) {
  width: 400px;
  height: 400px;
  animation-delay: 2s;
  opacity: 0.3;
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
}

/* تصميم متجاوب محسن */
@media (max-width: 1200px) {
  .container {
    max-width: 100%;
    padding: 1.5rem;
  }

  .card {
    margin: 1.5rem 0;
  }
}

@media (max-width: 768px) {
  body {
    padding: 2rem 1rem;
  }

  h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  .container {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1rem;
  }

  .card {
    padding: 2rem 1.5rem;
    margin: 1rem 0;
    border-radius: 20px;
  }

  .card h2 {
    font-size: 1.6rem;
  }

  .big {
    font-size: 1.4rem;
    line-height: 1.5;
    min-height: 3rem;
    padding: 0.5rem 0;
  }

  .note {
    font-size: 1rem;
  }

  /* تحسين الإيموجي للشاشات الصغيرة */
  .heart {
    font-size: 22px;
  }

  .particle {
    width: 4px;
    height: 4px;
  }
}

@media (max-width: 480px) {
  body {
    padding: 1.5rem 0.5rem;
  }

  h1 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .container {
    gap: 1.5rem;
    padding: 0.5rem;
  }

  .card {
    padding: 1.5rem 1rem;
    border-radius: 18px;
    margin: 0.8rem 0;
  }

  .card h2 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
  }

  .big {
    font-size: 1.2rem;
    line-height: 1.4;
    min-height: 2.8rem;
    padding: 0.3rem 0;
  }

  .note {
    font-size: 0.9rem;
    margin-top: 10px;
  }

  /* تقليل الإيموجي للشاشات الصغيرة جداً */
  .heart {
    font-size: 18px;
  }

  .particle {
    width: 3px;
    height: 3px;
  }
}

@media (max-width: 320px) {
  body {
    padding: 1rem 0.3rem;
  }

  h1 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .container {
    padding: 0.3rem;
    gap: 1rem;
  }

  .card {
    padding: 1.2rem 0.8rem;
    margin: 0.5rem 0;
  }

  .card h2 {
    font-size: 1.2rem;
  }

  .big {
    font-size: 1.1rem;
    min-height: 2.5rem;
    padding: 0.2rem 0;
  }

  .note {
    font-size: 0.8rem;
  }
}

/* تحسين للشاشات الكبيرة */
@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }

  h1 {
    font-size: 4rem;
  }

  .card {
    padding: 3rem 2.5rem;
  }

  .big {
    font-size: 2rem;
  }
}

/* تحسينات الأداء */
.fonts-loaded {
  font-display: swap;
}

.low-performance * {
  animation-duration: 0.1s !important;
  transition-duration: 0.1s !important;
}

.low-performance .heart,
.low-performance .particle {
  display: none;
}

.reduced-motion * {
  animation: none !important;
  transition: none !important;
}

.reduced-motion .heart,
.reduced-motion .particle,
.reduced-motion .pulse-ring {
  display: none;
}

/* تحسين الأداء للطباعة */
@media print {
  .floating-hearts,
  .music-particles,
  .pulse-ring {
    display: none;
  }

  body::before,
  body::after {
    display: none;
  }

  .card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

/* تحسين للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .heart,
  .particle {
    transform: translateZ(0);
    will-change: transform;
  }
}

/* تحسين استهلاك البطارية */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
