/* تصميم فاخر ورومانسي عالمي المستوى */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Cormorant+Garamond:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* ألوان رومانسية فاخرة */
    --luxury-rose: #ff6b9d;
    --deep-rose: #e91e63;
    --soft-pink: #ffc3e1;
    --luxury-gold: #d4af37;
    --rose-gold: #e8b4a0;
    --platinum: #e5e4e2;
    --deep-burgundy: #800020;
    --royal-purple: #663399;
    --midnight-navy: #1a1a2e;
    --pearl-white: #fefefe;
    --champagne: #f7e7ce;
    --lavender: #e6e6fa;

    /* تدرجات رومانسية فاخرة */
    --gradient-romantic: linear-gradient(135deg, #ff6b9d 0%, #ffc3e1 25%, #e8b4a0 50%, #d4af37 75%, #ff6b9d 100%);
    --gradient-sunset: linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    --gradient-royal: linear-gradient(135deg, #663399 0%, #800020 50%, #ff6b9d 100%);
    --gradient-pearl: linear-gradient(135deg, #fefefe 0%, #f7e7ce 50%, #e5e4e2 100%);
    --gradient-luxury: linear-gradient(45deg, #d4af37 0%, #ffd700 25%, #e8b4a0 50%, #ff6b9d 75%, #d4af37 100%);

    /* ظلال فاخرة */
    --shadow-romantic: 0 25px 50px rgba(255, 107, 157, 0.3);
    --shadow-luxury: 0 35px 70px rgba(212, 175, 55, 0.4);
    --shadow-glow: 0 0 30px rgba(255, 107, 157, 0.6);
    --shadow-deep: 0 40px 80px rgba(0, 0, 0, 0.3);
}

body {
    font-family: 'Cormorant Garamond', serif;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 107, 157, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 50% 10%, rgba(102, 51, 153, 0.1) 0%, transparent 60%),
        radial-gradient(circle at 10% 90%, rgba(255, 195, 225, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #16213e 75%, #1a1a2e 100%);
    background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 400% 400%;
    background-attachment: fixed;
    animation: romanticFlow 25s ease infinite;
    min-height: 100vh;
    color: var(--pearl-white);
    overflow-x: hidden;
    position: relative;
}

/* خلفية القلوب والنجوم المتلألئة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(3px 3px at 30px 40px, rgba(255, 107, 157, 0.8), transparent),
        radial-gradient(2px 2px at 80px 20px, rgba(212, 175, 55, 0.7), transparent),
        radial-gradient(2px 2px at 120px 80px, rgba(255, 255, 255, 0.6), transparent),
        radial-gradient(1px 1px at 160px 30px, rgba(255, 195, 225, 0.8), transparent),
        radial-gradient(2px 2px at 200px 60px, rgba(255, 107, 157, 0.6), transparent),
        radial-gradient(1px 1px at 50px 90px, rgba(212, 175, 55, 0.5), transparent);
    background-repeat: repeat;
    background-size: 250px 120px;
    animation: sparkleRomantic 12s linear infinite;
    pointer-events: none;
    z-index: 1;
}

/* الحاوية الرئيسية الفاخرة */
.main-container {
    position: relative;
    z-index: 2;
    max-width: 1600px;
    margin: 0 auto;
    padding: 4rem 3rem;
}

/* القسم الرئيسي الرومانسي */
.hero-section {
    text-align: center;
    margin-bottom: 6rem;
    padding: 6rem 0;
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: 40px;
    border: 2px solid rgba(255, 107, 157, 0.2);
    box-shadow: var(--shadow-romantic);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    background: var(--gradient-romantic);
    border-radius: 50%;
    opacity: 0.1;
    animation: pulseRomantic 8s ease-in-out infinite;
}

.luxury-crown {
    font-size: 6rem;
    background: var(--gradient-luxury);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 300% 300%;
    margin-bottom: 2rem;
    animation: crownGlow 4s ease-in-out infinite;
    filter: drop-shadow(0 0 25px rgba(255, 107, 157, 0.7));
}

.main-title {
    font-family: 'Playfair Display', serif;
    font-size: 5.5rem;
    font-weight: 800;
    background: var(--gradient-romantic);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 400% 400%;
    animation: romanticText 6s ease infinite;
    margin-bottom: 2rem;
    letter-spacing: 3px;
    position: relative;
}

.main-title::before,
.main-title::after {
    content: '💖';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 3rem;
    animation: heartFloat 3s ease-in-out infinite;
}

.main-title::before {
    left: -5rem;
}

.main-title::after {
    right: -5rem;
    animation-delay: 1.5s;
}

.subtitle {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    font-weight: 600;
    background: var(--gradient-sunset);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 3rem;
    animation: subtitleGlow 4s ease-in-out infinite;
    position: relative;
}

.subtitle::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 3px;
    background: var(--gradient-romantic);
    border-radius: 3px;
    animation: lineExpand 3s ease-out 1s both;
    box-shadow: 0 0 15px rgba(255, 107, 157, 0.5);
}

.decorative-ornament {
    width: 500px;
    height: 100px;
    margin: 0 auto;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 100"><path d="M0,50 Q125,20 250,50 Q375,80 500,50" stroke="%23ff6b9d" stroke-width="3" fill="none"/><circle cx="250" cy="50" r="12" fill="%23d4af37"/><circle cx="125" cy="35" r="6" fill="%23ff6b9d"/><circle cx="375" cy="65" r="6" fill="%23ff6b9d"/><path d="M230,30 L250,10 L270,30 L250,50 Z" fill="%23ffd700"/></svg>') center/contain no-repeat;
    animation: ornamentShine 5s ease-in-out infinite;
    filter: drop-shadow(0 0 20px rgba(255, 107, 157, 0.4));
}

/* شبكة المحتوى الفاخرة */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 3rem;
    margin-bottom: 5rem;
    padding: 2rem 0;
}

/* المربعات الفاخرة والرومانسية */
.luxury-card {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%),
        rgba(26, 26, 46, 0.8);
    backdrop-filter: blur(25px);
    border: 3px solid transparent;
    background-clip: padding-box;
    border-radius: 35px;
    padding: 4rem 3.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow:
        var(--shadow-romantic),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* إطار ذهبي متحرك حول المربعات */
.luxury-card::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: var(--gradient-romantic);
    border-radius: 35px;
    z-index: -1;
    animation: borderGlow 4s ease-in-out infinite;
}

/* تأثير الضوء المتحرك */
.luxury-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent,
        rgba(255, 107, 157, 0.2),
        transparent,
        rgba(212, 175, 55, 0.2),
        transparent
    );
    animation: cardRotate 12s linear infinite;
    opacity: 0;
    transition: opacity 0.8s ease;
}

.luxury-card:hover::after {
    opacity: 1;
}

.luxury-card:hover {
    transform: translateY(-25px) scale(1.05) rotateX(5deg);
    box-shadow:
        var(--shadow-luxury),
        var(--shadow-glow),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 107, 157, 0.5);
}

/* أيقونات المربعات */
.card-icon {
    font-size: 5rem;
    margin-bottom: 2rem;
    display: block;
    filter: drop-shadow(0 0 20px rgba(255, 107, 157, 0.8));
    animation: iconFloat 4s ease-in-out infinite;
    position: relative;
}

.card-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(255, 107, 157, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    animation: iconGlow 3s ease-in-out infinite;
    z-index: -1;
}

/* عناوين المربعات */
.card-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--gradient-luxury);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 300% 300%;
    animation: titleShine 5s ease infinite;
    margin-bottom: 2rem;
    position: relative;
}

.card-title::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-romantic);
    border-radius: 4px;
    box-shadow: 0 0 15px rgba(255, 107, 157, 0.6);
}

/* تاريخ المربعات */
.card-date {
    color: var(--soft-pink);
    font-size: 1.4rem;
    margin-bottom: 2.5rem;
    font-weight: 500;
    opacity: 0.9;
    font-family: 'Dancing Script', cursive;
    text-shadow: 0 0 10px rgba(255, 195, 225, 0.5);
}

/* العدادات الفاخرة */
.luxury-counter {
    background: var(--gradient-pearl);
    border-radius: 25px;
    padding: 2.5rem 2rem;
    margin: 2.5rem 0;
    color: var(--midnight-navy);
    font-size: 1.8rem;
    font-weight: 700;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        inset 0 2px 10px rgba(0, 0, 0, 0.1),
        0 10px 30px rgba(255, 107, 157, 0.2);
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 107, 157, 0.3);
}

.luxury-counter::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.4), transparent);
    transition: left 1s ease;
}

.luxury-card:hover .luxury-counter::before {
    left: 100%;
}

/* رسائل المربعات */
.card-message {
    color: var(--champagne);
    font-style: italic;
    font-size: 1.3rem;
    opacity: 0.95;
    font-family: 'Dancing Script', cursive;
    text-shadow: 0 0 8px rgba(247, 231, 206, 0.4);
    line-height: 1.6;
}

/* أنواع المربعات المختلفة */
.primary-luxury {
    background:
        linear-gradient(135deg, rgba(255, 107, 157, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%),
        rgba(26, 26, 46, 0.9);
}

.primary-luxury::before {
    background: linear-gradient(45deg, #ff6b9d, #d4af37, #ff6b9d);
}

.special-luxury {
    background:
        linear-gradient(135deg, rgba(212, 175, 55, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%),
        rgba(26, 26, 46, 0.85);
}

.special-luxury::before {
    background: linear-gradient(45deg, #d4af37, #ff6b9d, #ffd700);
}

.promise-luxury {
    background:
        linear-gradient(135deg, rgba(102, 51, 153, 0.15) 0%, rgba(128, 0, 32, 0.1) 100%),
        rgba(26, 26, 46, 0.9);
}

.promise-luxury::before {
    background: linear-gradient(45deg, #663399, #800020, #ff6b9d);
}

/* عناصر خاصة */
.special-fact {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin: 2.5rem 0;
    padding: 1.5rem;
    background: rgba(255, 107, 157, 0.1);
    border-radius: 20px;
    border: 2px solid rgba(255, 107, 157, 0.3);
}

.fact-number {
    font-size: 5rem;
    font-weight: 900;
    background: var(--gradient-luxury);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 15px rgba(212, 175, 55, 0.7));
    animation: numberPulse 3s ease-in-out infinite;
}

.fact-text {
    font-size: 1.6rem;
    color: var(--soft-pink);
    font-weight: 600;
    text-shadow: 0 0 10px rgba(255, 195, 225, 0.5);
}

.promise-text {
    font-size: 1.7rem;
    background: var(--gradient-sunset);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    line-height: 1.8;
    margin: 2.5rem 0;
    font-family: 'Playfair Display', serif;
    text-align: center;
}

.love-message {
    font-size: 1.5rem;
    background: var(--gradient-romantic);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 300% 300%;
    animation: messageGlow 4s ease infinite;
    font-style: italic;
    line-height: 1.8;
    margin: 2.5rem 0;
    position: relative;
    font-family: 'Cormorant Garamond', serif;
    padding: 1rem;
}

.love-message::before,
.love-message::after {
    content: '"';
    font-size: 4rem;
    color: var(--luxury-gold);
    font-family: 'Playfair Display', serif;
    position: absolute;
    filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.6));
}

.love-message::before {
    top: -15px;
    left: -25px;
}

.love-message::after {
    bottom: -35px;
    right: -25px;
}

/* التذييل الفاخر */
.luxury-footer {
    text-align: center;
    padding: 5rem 0;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: 40px;
    border: 2px solid rgba(255, 107, 157, 0.2);
    margin-top: 4rem;
    box-shadow: var(--shadow-romantic);
}

.footer-ornament {
    font-size: 4rem;
    margin-bottom: 2rem;
    background: var(--gradient-luxury);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 300% 300%;
    animation: ornamentSpin 8s ease-in-out infinite;
    filter: drop-shadow(0 0 20px rgba(255, 107, 157, 0.6));
}

.footer-text {
    font-size: 1.5rem;
    background: var(--gradient-sunset);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 500;
    font-family: 'Dancing Script', cursive;
}

/* الأنيميشن الفاخر والرومانسي */
@keyframes romanticFlow {
    0%, 100% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg) brightness(1);
    }
    25% {
        background-position: 25% 75%;
        filter: hue-rotate(10deg) brightness(1.1);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(20deg) brightness(1.2);
    }
    75% {
        background-position: 75% 25%;
        filter: hue-rotate(10deg) brightness(1.1);
    }
}

@keyframes sparkleRomantic {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-15px) scale(1.2);
        opacity: 1;
    }
}

@keyframes pulseRomantic {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.2;
    }
}

@keyframes crownGlow {
    0%, 100% {
        filter: drop-shadow(0 0 25px rgba(255, 107, 157, 0.7)) scale(1);
        background-position: 0% 50%;
    }
    50% {
        filter: drop-shadow(0 0 45px rgba(212, 175, 55, 0.9)) scale(1.05);
        background-position: 100% 50%;
    }
}

@keyframes romanticText {
    0%, 100% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    50% {
        background-position: 100% 50%;
        transform: scale(1.02);
    }
}

@keyframes heartFloat {
    0%, 100% {
        transform: translateY(-50%) scale(1) rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-60%) scale(1.2) rotate(10deg);
        opacity: 1;
    }
}

@keyframes subtitleGlow {
    0%, 100% {
        filter: drop-shadow(0 0 10px rgba(255, 107, 157, 0.5));
        background-position: 0% 50%;
    }
    50% {
        filter: drop-shadow(0 0 25px rgba(255, 107, 157, 0.8));
        background-position: 100% 50%;
    }
}

@keyframes lineExpand {
    0% {
        width: 0;
        opacity: 0;
        box-shadow: none;
    }
    100% {
        width: 300px;
        opacity: 1;
        box-shadow: 0 0 15px rgba(255, 107, 157, 0.5);
    }
}

@keyframes ornamentShine {
    0%, 100% {
        filter: drop-shadow(0 0 20px rgba(255, 107, 157, 0.4));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 35px rgba(212, 175, 55, 0.7));
        transform: scale(1.05);
    }
}

@keyframes borderGlow {
    0%, 100% {
        opacity: 0.6;
        filter: blur(1px);
    }
    50% {
        opacity: 1;
        filter: blur(0px);
    }
}

@keyframes cardRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        filter: drop-shadow(0 0 20px rgba(255, 107, 157, 0.8));
    }
    50% {
        transform: translateY(-15px) rotate(5deg);
        filter: drop-shadow(0 0 35px rgba(212, 175, 55, 0.9));
    }
}

@keyframes iconGlow {
    0%, 100% {
        opacity: 0.2;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@keyframes titleShine {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes numberPulse {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 15px rgba(212, 175, 55, 0.7));
    }
    50% {
        transform: scale(1.1);
        filter: drop-shadow(0 0 30px rgba(255, 107, 157, 0.9));
    }
}

@keyframes messageGlow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes ornamentSpin {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        background-position: 0% 50%;
    }
    50% {
        transform: rotate(180deg) scale(1.1);
        background-position: 100% 50%;
    }
}

/* أنيميشن الدخول المتدرج للمربعات */
.luxury-card:nth-child(1) { animation: fadeInUp 1.2s ease-out 0.3s both; }
.luxury-card:nth-child(2) { animation: fadeInUp 1.2s ease-out 0.6s both; }
.luxury-card:nth-child(3) { animation: fadeInUp 1.2s ease-out 0.9s both; }
.luxury-card:nth-child(4) { animation: fadeInUp 1.2s ease-out 1.2s both; }
.luxury-card:nth-child(5) { animation: fadeInUp 1.2s ease-out 1.5s both; }
.luxury-card:nth-child(6) { animation: fadeInUp 1.2s ease-out 1.8s both; }

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(80px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* التصميم المتجاوب الفاخر */
@media (max-width: 1400px) {
    .main-container {
        max-width: 1200px;
        padding: 3rem 2rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2.5rem;
    }
}

@media (max-width: 1024px) {
    .main-container {
        padding: 2rem 1.5rem;
    }

    .hero-section {
        padding: 4rem 0;
        margin-bottom: 4rem;
    }

    .main-title {
        font-size: 4rem;
    }

    .luxury-crown {
        font-size: 4.5rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
    }

    .luxury-card {
        padding: 3rem 2.5rem;
    }
}

@media (max-width: 768px) {
    .main-container {
        padding: 1.5rem 1rem;
    }

    .hero-section {
        padding: 3rem 0;
        margin-bottom: 3rem;
        border-radius: 30px;
    }

    .main-title {
        font-size: 3rem;
        letter-spacing: 2px;
    }

    .main-title::before,
    .main-title::after {
        font-size: 2rem;
        left: -3rem;
        right: -3rem;
    }

    .subtitle {
        font-size: 2rem;
    }

    .luxury-crown {
        font-size: 3.5rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .luxury-card {
        padding: 3rem 2rem;
        border-radius: 25px;
    }

    .card-title {
        font-size: 2rem;
    }

    .luxury-counter {
        font-size: 1.5rem;
        padding: 2rem 1.5rem;
        min-height: 100px;
    }

    .card-icon {
        font-size: 4rem;
    }

    .fact-number {
        font-size: 4rem;
    }

    .promise-text,
    .love-message {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .main-container {
        padding: 1rem 0.5rem;
    }

    .main-title {
        font-size: 2.2rem;
        letter-spacing: 1px;
    }

    .main-title::before,
    .main-title::after {
        display: none;
    }

    .subtitle {
        font-size: 1.6rem;
    }

    .luxury-crown {
        font-size: 2.8rem;
    }

    .luxury-card {
        padding: 2.5rem 1.5rem;
        border-radius: 20px;
    }

    .card-title {
        font-size: 1.7rem;
    }

    .luxury-counter {
        font-size: 1.2rem;
        padding: 1.5rem;
        min-height: 80px;
    }

    .card-icon {
        font-size: 3rem;
    }

    .fact-number {
        font-size: 3rem;
    }

    .decorative-ornament {
        width: 300px;
        height: 60px;
    }

    .special-fact {
        flex-direction: column;
        gap: 1rem;
    }
}

/* تحسينات الأداء */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (max-width: 768px) {
    body::before {
        background-size: 150px 80px;
    }
}
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Cormorant+Garamond:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* ألوان فاخرة */
    --luxury-gold: #d4af37;
    --deep-gold: #b8860b;
    --rose-gold: #e8b4a0;
    --platinum: #e5e4e2;
    --deep-burgundy: #800020;
    --royal-purple: #663399;
    --midnight-black: #0c0c0c;
    --pearl-white: #fefefe;
    --champagne: #f7e7ce;
    --diamond-silver: #c0c0c0;
    
    /* تدرجات فاخرة */
    --gradient-luxury: linear-gradient(135deg, #d4af37 0%, #ffd700 25%, #e8b4a0 50%, #d4af37 75%, #b8860b 100%);
    --gradient-royal: linear-gradient(45deg, #663399 0%, #800020 50%, #663399 100%);
    --gradient-pearl: linear-gradient(135deg, #fefefe 0%, #f7e7ce 50%, #e5e4e2 100%);
    --gradient-diamond: radial-gradient(circle, #ffffff 0%, #e5e4e2 50%, #c0c0c0 100%);
    
    /* ظلال فاخرة */
    --shadow-luxury: 0 25px 50px rgba(212, 175, 55, 0.3);
    --shadow-deep: 0 35px 70px rgba(0, 0, 0, 0.4);
    --shadow-glow: 0 0 30px rgba(212, 175, 55, 0.5);
    --shadow-inner: inset 0 2px 10px rgba(255, 255, 255, 0.3);
}

body {
    font-family: 'Cormorant Garamond', serif;
    background: 
        radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(102, 51, 153, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(128, 0, 32, 0.05) 0%, transparent 70%),
        linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 25%, #2d2d2d 50%, #1a1a1a 75%, #0c0c0c 100%);
    background-size: 100% 100%, 100% 100%, 100% 100%, 400% 400%;
    background-attachment: fixed;
    animation: luxuryGradient 20s ease infinite;
    min-height: 100vh;
    color: var(--pearl-white);
    overflow-x: hidden;
    position: relative;
}

/* خلفية الماس المتلألئة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(2px 2px at 20px 30px, rgba(212, 175, 55, 0.8), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.6), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(212, 175, 55, 0.5), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.4), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(212, 175, 55, 0.7), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 8s linear infinite;
    pointer-events: none;
    z-index: 1;
}

/* الحاوية الرئيسية الفاخرة */
.main-container {
    position: relative;
    z-index: 2;
    max-width: 1600px;
    margin: 0 auto;
    padding: 4rem 3rem;
}

/* القسم الرئيسي الفاخر */
.hero-section {
    text-align: center;
    margin-bottom: 6rem;
    padding: 6rem 0;
    position: relative;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    background: var(--gradient-diamond);
    border-radius: 50%;
    opacity: 0.1;
    animation: rotate 30s linear infinite;
}

.luxury-crown {
    font-size: 6rem;
    background: var(--gradient-luxury);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2rem;
    animation: crownGlow 3s ease-in-out infinite;
    filter: drop-shadow(0 0 20px rgba(212, 175, 55, 0.6));
}

.main-title {
    font-family: 'Playfair Display', serif;
    font-size: 5.5rem;
    font-weight: 800;
    background: var(--gradient-luxury);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 300% 300%;
    animation: luxuryText 4s ease infinite;
    margin-bottom: 2rem;
    letter-spacing: 3px;
    text-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
    position: relative;
}

.main-title::before,
.main-title::after {
    content: '✦';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2rem;
    color: var(--luxury-gold);
    animation: sparkleRotate 4s ease-in-out infinite;
}

.main-title::before {
    left: -4rem;
}

.main-title::after {
    right: -4rem;
    animation-delay: 2s;
}

.subtitle {
    font-family: 'Dancing Script', cursive;
    font-size: 2.2rem;
    font-weight: 600;
    color: var(--champagne);
    margin-bottom: 3rem;
    opacity: 0.9;
    animation: fadeInUp 2s ease-out 0.5s both;
    position: relative;
}

.subtitle::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 2px;
    background: var(--gradient-luxury);
    border-radius: 2px;
    animation: expandLine 2s ease-out 1s both;
}

.decorative-ornament {
    width: 400px;
    height: 80px;
    margin: 0 auto;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 80"><path d="M0,40 Q100,10 200,40 Q300,70 400,40" stroke="%23d4af37" stroke-width="2" fill="none"/><circle cx="200" cy="40" r="8" fill="%23d4af37"/><circle cx="100" cy="30" r="4" fill="%23ffd700"/><circle cx="300" cy="50" r="4" fill="%23ffd700"/></svg>') center/contain no-repeat;
    animation: ornamentGlow 3s ease-in-out infinite;
}

/* شبكة المحتوى الفاخرة */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    margin-bottom: 4rem;
}

/* كروت فاخرة */
.luxury-card {
    background: 
        linear-gradient(135deg, rgba(254, 254, 254, 0.1) 0%, rgba(229, 228, 226, 0.05) 100%),
        rgba(12, 12, 12, 0.8);
    backdrop-filter: blur(20px);
    border: 2px solid transparent;
    background-clip: padding-box;
    border-radius: 30px;
    padding: 3.5rem 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: var(--shadow-luxury);
}

.luxury-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-luxury);
    border-radius: 30px;
    padding: 2px;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
    opacity: 0.6;
}

.luxury-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    animation: cardRotate 8s linear infinite;
    opacity: 0;
    transition: opacity 0.6s ease;
}

.luxury-card:hover::after {
    opacity: 1;
}

.luxury-card:hover {
    transform: translateY(-20px) scale(1.03);
    box-shadow: 
        var(--shadow-deep),
        var(--shadow-glow),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.card-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    display: block;
    filter: drop-shadow(0 0 15px rgba(212, 175, 55, 0.6));
    animation: iconFloat 3s ease-in-out infinite;
}

.card-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.2rem;
    font-weight: 700;
    background: var(--gradient-luxury);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    position: relative;
}

.card-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: var(--gradient-luxury);
    border-radius: 2px;
}

.card-date {
    color: var(--champagne);
    font-size: 1.3rem;
    margin-bottom: 2rem;
    font-weight: 500;
    opacity: 0.8;
}

.luxury-counter {
    background: var(--gradient-pearl);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    color: var(--midnight-black);
    font-size: 1.6rem;
    font-weight: 600;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-inner);
    position: relative;
    overflow: hidden;
}

.luxury-counter::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.3), transparent);
    transition: left 0.8s ease;
}

.luxury-card:hover .luxury-counter::before {
    left: 100%;
}

.card-message {
    color: var(--platinum);
    font-style: italic;
    font-size: 1.1rem;
    opacity: 0.9;
    font-family: 'Dancing Script', cursive;
}

/* كروت خاصة فاخرة */
.primary-luxury {
    border-image: var(--gradient-luxury) 1;
}

.special-luxury {
    background: 
        linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(254, 254, 254, 0.05) 100%),
        rgba(12, 12, 12, 0.9);
}

.promise-luxury {
    background: 
        linear-gradient(135deg, rgba(102, 51, 153, 0.1) 0%, rgba(128, 0, 32, 0.1) 100%),
        rgba(12, 12, 12, 0.9);
}

.special-fact {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin: 2rem 0;
}

.fact-number {
    font-size: 4rem;
    font-weight: 800;
    background: var(--gradient-luxury);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.5));
}

.fact-text {
    font-size: 1.4rem;
    color: var(--champagne);
    font-weight: 500;
}

.promise-text {
    font-size: 1.5rem;
    color: var(--platinum);
    font-weight: 500;
    line-height: 1.8;
    margin: 2rem 0;
    font-family: 'Playfair Display', serif;
}

.love-message {
    font-size: 1.4rem;
    color: var(--champagne);
    font-style: italic;
    line-height: 1.8;
    margin: 2rem 0;
    position: relative;
    font-family: 'Cormorant Garamond', serif;
}

.love-message::before,
.love-message::after {
    content: '"';
    font-size: 3rem;
    color: var(--luxury-gold);
    font-family: 'Playfair Display', serif;
    position: absolute;
}

.love-message::before {
    top: -10px;
    left: -20px;
}

.love-message::after {
    bottom: -30px;
    right: -20px;
}

/* التذييل الفاخر */
.luxury-footer {
    text-align: center;
    padding: 4rem 0;
    border-top: 1px solid rgba(212, 175, 55, 0.3);
    margin-top: 4rem;
}

.footer-ornament {
    font-size: 3rem;
    margin-bottom: 2rem;
    background: var(--gradient-luxury);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: ornamentSpin 6s ease-in-out infinite;
}

.footer-text {
    font-size: 1.3rem;
    color: var(--champagne);
    font-weight: 400;
    font-family: 'Dancing Script', cursive;
}

/* الأنيميشن الفاخر */
@keyframes luxuryGradient {
    0%, 100% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg) brightness(1);
    }
    25% {
        background-position: 25% 75%;
        filter: hue-rotate(15deg) brightness(1.1);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(30deg) brightness(1.2);
    }
    75% {
        background-position: 75% 25%;
        filter: hue-rotate(15deg) brightness(1.1);
    }
}

@keyframes sparkle {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) scale(1.1);
        opacity: 1;
    }
}

@keyframes rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes crownGlow {
    0%, 100% {
        filter: drop-shadow(0 0 20px rgba(212, 175, 55, 0.6)) scale(1);
    }
    50% {
        filter: drop-shadow(0 0 40px rgba(212, 175, 55, 0.9)) scale(1.05);
    }
}

@keyframes luxuryText {
    0%, 100% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    50% {
        background-position: 100% 50%;
        transform: scale(1.02);
    }
}

@keyframes sparkleRotate {
    0%, 100% {
        transform: translateY(-50%) rotate(0deg) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-50%) rotate(180deg) scale(1.2);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(60px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes expandLine {
    0% {
        width: 0;
        opacity: 0;
    }
    100% {
        width: 200px;
        opacity: 1;
    }
}

@keyframes ornamentGlow {
    0%, 100% {
        filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.5));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 25px rgba(212, 175, 55, 0.8));
        transform: scale(1.05);
    }
}

@keyframes cardRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        filter: drop-shadow(0 0 15px rgba(212, 175, 55, 0.6));
    }
    50% {
        transform: translateY(-10px) rotate(5deg);
        filter: drop-shadow(0 0 25px rgba(212, 175, 55, 0.9));
    }
}

@keyframes ornamentSpin {
    0%, 100% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
    }
}

/* أنيميشن الدخول المتدرج */
.luxury-card:nth-child(1) { animation: fadeInUp 1s ease-out 0.2s both; }
.luxury-card:nth-child(2) { animation: fadeInUp 1s ease-out 0.4s both; }
.luxury-card:nth-child(3) { animation: fadeInUp 1s ease-out 0.6s both; }
.luxury-card:nth-child(4) { animation: fadeInUp 1s ease-out 0.8s both; }
.luxury-card:nth-child(5) { animation: fadeInUp 1s ease-out 1.0s both; }
.luxury-card:nth-child(6) { animation: fadeInUp 1s ease-out 1.2s both; }

/* التصميم المتجاوب الفاخر */
@media (max-width: 1400px) {
    .main-container {
        max-width: 1200px;
        padding: 3rem 2rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2.5rem;
    }
}

@media (max-width: 1024px) {
    .main-container {
        padding: 2rem 1.5rem;
    }

    .hero-section {
        padding: 4rem 0;
        margin-bottom: 4rem;
    }

    .main-title {
        font-size: 4rem;
    }

    .luxury-crown {
        font-size: 4.5rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .luxury-card {
        padding: 3rem 2.5rem;
    }
}

@media (max-width: 768px) {
    .main-container {
        padding: 1.5rem 1rem;
    }

    .hero-section {
        padding: 3rem 0;
        margin-bottom: 3rem;
    }

    .main-title {
        font-size: 3rem;
        letter-spacing: 2px;
    }

    .main-title::before,
    .main-title::after {
        display: none;
    }

    .subtitle {
        font-size: 1.8rem;
    }

    .luxury-crown {
        font-size: 3.5rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .luxury-card {
        padding: 2.5rem 2rem;
        border-radius: 25px;
    }

    .card-title {
        font-size: 1.8rem;
    }

    .luxury-counter {
        font-size: 1.3rem;
        padding: 1.5rem;
        min-height: 80px;
    }

    .card-icon {
        font-size: 3rem;
    }

    .fact-number {
        font-size: 3rem;
    }

    .promise-text,
    .love-message {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .main-container {
        padding: 1rem 0.5rem;
    }

    .main-title {
        font-size: 2.2rem;
        letter-spacing: 1px;
    }

    .subtitle {
        font-size: 1.5rem;
    }

    .luxury-crown {
        font-size: 2.8rem;
    }

    .luxury-card {
        padding: 2rem 1.5rem;
        border-radius: 20px;
    }

    .card-title {
        font-size: 1.5rem;
    }

    .luxury-counter {
        font-size: 1.1rem;
        padding: 1.2rem;
        min-height: 70px;
    }

    .card-icon {
        font-size: 2.5rem;
    }

    .fact-number {
        font-size: 2.5rem;
    }

    .decorative-ornament {
        width: 250px;
        height: 50px;
    }
}

/* تحسينات الأداء */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (max-width: 768px) {
    body::before {
        display: none;
    }
}
