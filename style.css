/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-pink: #ff6b9d;
    --secondary-pink: #ffc3e1;
    --accent-gold: #ffd700;
    --soft-purple: #c44569;
    --gentle-blue: #74b9ff;
    --pure-white: #ffffff;
    --soft-gray: #f8f9fa;
    --text-dark: #2d3436;
    --shadow-light: rgba(255, 107, 157, 0.2);
    --shadow-medium: rgba(255, 107, 157, 0.4);
    --gradient-romantic: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink), var(--gentle-blue));
    --gradient-sunset: linear-gradient(45deg, #ff9a9e, #fecfef, #fecfef);
}

body {
    font-family: 'Cairo', 'Tahoma', sans-serif;
    background: var(--gradient-romantic);
    background-size: 400% 400%;
    animation: gradientFlow 20s ease infinite;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* خلفية الجزيئات المتحركة */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--pure-white);
    border-radius: 50%;
    opacity: 0.6;
    animation: float 8s linear infinite;
}

/* الحاوية الرئيسية */
.main-container {
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* القسم الرئيسي */
.hero-section {
    text-align: center;
    margin-bottom: 4rem;
    padding: 3rem 0;
}

.love-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: heartbeat 2s ease-in-out infinite;
}

.main-title {
    font-family: 'Amiri', serif;
    font-size: 4rem;
    font-weight: 700;
    color: var(--pure-white);
    text-shadow: 2px 2px 10px var(--shadow-medium);
    margin-bottom: 1rem;
    letter-spacing: 2px;
}

.subtitle {
    font-size: 1.5rem;
    color: var(--pure-white);
    opacity: 0.9;
    margin-bottom: 2rem;
    font-weight: 300;
}

.decorative-line {
    width: 200px;
    height: 3px;
    background: var(--gradient-sunset);
    margin: 0 auto;
    border-radius: 2px;
    box-shadow: 0 2px 10px var(--shadow-light);
}

/* شبكة المحتوى */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* كروت الحب */
.love-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: 
        0 20px 40px rgba(255, 107, 157, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.love-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
}

.love-card:hover::before {
    left: 100%;
}

.love-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 
        0 30px 60px rgba(255, 107, 157, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.3);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.card-title {
    font-family: 'Amiri', serif;
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

.card-date {
    color: var(--soft-purple);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.counter-display {
    background: var(--gradient-sunset);
    border-radius: 15px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    color: var(--text-dark);
    font-size: 1.4rem;
    font-weight: 600;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card-message {
    color: var(--soft-purple);
    font-style: italic;
    font-size: 1rem;
    opacity: 0.8;
}

/* كروت خاصة */
.primary-card {
    border: 2px solid var(--primary-pink);
}

.birthday-card .card-icon {
    animation: bounce 2s ease-in-out infinite;
}

.special-card {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 255, 255, 0.95));
}

.special-fact {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin: 1.5rem 0;
}

.fact-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-gold);
    text-shadow: 2px 2px 5px var(--shadow-light);
}

.fact-text {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-weight: 500;
}

.promise-card {
    background: linear-gradient(135deg, rgba(196, 69, 105, 0.1), rgba(255, 255, 255, 0.95));
}

.promise-text {
    font-size: 1.3rem;
    color: var(--soft-purple);
    font-weight: 500;
    line-height: 1.6;
    margin: 1.5rem 0;
}

.message-card {
    background: linear-gradient(135deg, rgba(116, 185, 255, 0.1), rgba(255, 255, 255, 0.95));
}

.love-message {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-style: italic;
    line-height: 1.6;
    margin: 1.5rem 0;
    position: relative;
}

.love-message::before,
.love-message::after {
    content: '"';
    font-size: 2rem;
    color: var(--primary-pink);
    font-family: serif;
}

/* التذييل */
.romantic-footer {
    text-align: center;
    padding: 2rem 0;
    color: var(--pure-white);
}

.footer-hearts {
    font-size: 2rem;
    margin-bottom: 1rem;
    animation: heartbeat 3s ease-in-out infinite;
}

.footer-text {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* قلب التحميل */
.loading-heart {
    font-size: 2rem;
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* الأنيميشن */
@keyframes gradientFlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes float {
    0% {
        transform: translateY(100vh) translateX(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(50px) rotate(360deg);
        opacity: 0;
    }
}

/* التصميم المتجاوب */
@media (max-width: 1200px) {
    .main-container {
        padding: 1.5rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .main-container {
        padding: 1rem;
    }

    .hero-section {
        padding: 2rem 0;
        margin-bottom: 2rem;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .subtitle {
        font-size: 1.2rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .love-card {
        padding: 2rem 1.5rem;
    }

    .card-title {
        font-size: 1.5rem;
    }

    .counter-display {
        font-size: 1.2rem;
        padding: 1rem;
        min-height: 60px;
    }

    .fact-number {
        font-size: 2.5rem;
    }

    .promise-text,
    .love-message {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .main-container {
        padding: 0.5rem;
    }

    .main-title {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .love-card {
        padding: 1.5rem 1rem;
        border-radius: 20px;
    }

    .card-title {
        font-size: 1.3rem;
    }

    .counter-display {
        font-size: 1rem;
        padding: 0.8rem;
        min-height: 50px;
    }

    .card-icon {
        font-size: 2.5rem;
    }

    .love-icon {
        font-size: 3rem;
    }
}

/* تحسينات الأداء */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (max-width: 768px) {
    .particle {
        display: none;
    }
}
